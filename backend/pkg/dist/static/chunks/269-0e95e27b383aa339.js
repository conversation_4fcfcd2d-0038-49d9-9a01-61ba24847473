"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[269],{280:(e,t,n)=>{n.d(t,{lG:()=>eQ});var r,o,l,i=n(2115),a=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(a||{}),u=n(6232);function s(e,t,n,r){let o=(0,u.Y)(n);(0,i.useEffect)(()=>{function n(e){o.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class c extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}constructor(e){super(),this.factory=e}}var d=n(5261),f=Object.defineProperty,p=(e,t,n)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,v=(e,t,n)=>(p(e,"symbol"!=typeof t?t+"":t,n),n),h=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},m=(e,t,n)=>(h(e,t,"read from private field"),n?n.call(e):t.get(e)),g=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},b=(e,t,n,r)=>(h(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class y{dispose(){this.disposables.dispose()}get state(){return m(this,r)}subscribe(e,t){let n={selector:e,callback:t,current:e(m(this,r))};return m(this,l).add(n),this.disposables.add(()=>{m(this,l).delete(n)})}on(e,t){return m(this,o).get(e).add(t),this.disposables.add(()=>{m(this,o).get(e).delete(t)})}send(e){let t=this.reduce(m(this,r),e);if(t!==m(this,r)){for(let e of(b(this,r,t),m(this,l))){let t=e.selector(m(this,r));E(e.current,t)||(e.current=t,e.callback(t))}for(let t of m(this,o).get(e.type))t(m(this,r),e)}}constructor(e){g(this,r,{}),g(this,o,new c(()=>new Set)),g(this,l,new Set),v(this,"disposables",(0,d.e)()),b(this,r,e)}}function E(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&w(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&w(e.entries(),t.entries()):!!(F(e)&&F(t))&&w(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function w(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function F(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}r=new WeakMap,o=new WeakMap,l=new WeakMap;var P=n(7279),S=Object.defineProperty,C=(e,t,n)=>t in e?S(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,A=(e,t,n)=>(C(e,"symbol"!=typeof t?t+"":t,n),n),k=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(k||{});let O={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}};class x extends y{static new(){return new x({stack:[]})}reduce(e,t){return(0,P.Y)(t.type,O,e,t)}constructor(){super(...arguments),A(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),A(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}}let T=new c(()=>x.new());var R=n(1992),_=n(797);function N(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:E;return(0,R.useSyncExternalStoreWithSelector)((0,_._)(t=>e.subscribe(L,t)),(0,_._)(()=>e.state),(0,_._)(()=>e.state),(0,_._)(t),n)}function L(e){return e}var M=n(1231);function D(e,t){let n=(0,i.useId)(),r=T.get(t),[o,l]=N(r,(0,i.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return(0,M.s)(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!l||o)}var j=n(7657);function I(e){var t,n;return j._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let U=new Map,Y=new Map;function H(e){var t;let n=null!=(t=Y.get(e))?t:0;return Y.set(e,n+1),0!==n||(U.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=Y.get(e))?t:1;if(1===n?Y.delete(e):Y.set(e,n-1),1!==n)return;let r=U.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,U.delete(e))})(e)}function W(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function X(e){return W(e)&&"tagName"in e}function V(e){return X(e)&&"accessKey"in e}function K(e){return X(e)&&"tabIndex"in e}let B=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),q=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var G=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(G||{}),z=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(z||{}),$=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))($||{}),Z=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Z||{}),J=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(J||{});function Q(e){null==e||e.focus({preventScroll:!0})}function ee(e,t){var n,r,o;let{sorted:l=!0,relativeTo:i=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?l?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(q)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(B)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);a.length>0&&s.length>1&&(s=s.filter(e=>!a.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),i=null!=i?i:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(i))-1;if(4&t)return Math.max(0,s.indexOf(i))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,v=s.length,h;do{if(p>=v||p+v<=0)return 0;let e=d+p;if(16&t)e=(e+v)%v;else{if(e<0)return 3;if(e>=v)return 1}null==(h=s[e])||h.focus(f),p+=c}while(h!==u.activeElement);return 6&t&&null!=(o=null==(r=null==(n=h)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&o&&h.select(),2}function et(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function en(){return et()||/Android/gi.test(window.navigator.userAgent)}function er(e,t,n,r){let o=(0,u.Y)(n);(0,i.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function eo(e,t,n,r){let o=(0,u.Y)(n);(0,i.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function el(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.useMemo)(()=>I(...t),[...t])}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var ei=n(4554),ea=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ea||{});let eu=(0,ei.FX)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,ei.Ci)()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}),es=(0,i.createContext)(null);function ec(e){let{children:t,node:n}=e,[r,o]=(0,i.useState)(null),l=ed(null!=n?n:r);return i.createElement(es.Provider,{value:l},t,null===l&&i.createElement(eu,{features:ea.Hidden,ref:e=>{var t,n;if(e){for(let r of null!=(n=null==(t=I(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&X(r)&&null!=r&&r.contains(e)){o(r);break}}}}))}function ed(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null!=(e=(0,i.useContext)(es))?e:t}let ef=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var o=arguments.length,l=Array(o>1?o-1:0),i=1;i<o;i++)l[i-1]=arguments[i];let a=t[e].call(n,...l);a&&(n=a,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,d.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:o}=e,l={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(o)},i=[et()?{before(e){let{doc:t,d:n,meta:r}=e;function o(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=(0,d.e)();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,l=null;n.addEventListener(t,"click",e=>{if(K(e.target))try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),i=t.querySelector(r);K(i)&&!o(i)&&(l=i)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{var t;if(K(e.target)&&X(t=e.target)&&"style"in t)if(o(e.target)){let t=e.target;for(;t.parentElement&&o(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(K(e.target)){var t;if(!(V(t=e.target)&&"INPUT"===t.nodeName))if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before(e){var n;let{doc:r}=e,o=r.documentElement;t=Math.max(0,(null!=(n=r.defaultView)?n:window).innerWidth-o.clientWidth)},after(e){let{doc:n,d:r}=e,o=n.documentElement,l=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,t-l);r.style(o,"paddingRight","".concat(i,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];i.forEach(e=>{let{before:t}=e;return null==t?void 0:t(l)}),i.forEach(e=>{let{after:t}=e;return null==t?void 0:t(l)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});ef.subscribe(()=>{let e=ef.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&ef.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&ef.dispatch("TEARDOWN",n)}});var ep=n(9925),ev=n(7769);let eh=(0,i.createContext)(()=>{});function em(e){let{value:t,children:n}=e;return i.createElement(eh.Provider,{value:t},n)}var eg=n(1525);let eb=(0,i.createContext)(!1);function ey(e){return i.createElement(eb.Provider,{value:e.force},e.children)}let eE=(0,i.createContext)(void 0),ew=(0,i.createContext)(null);ew.displayName="DescriptionContext";let eF=Object.assign((0,ei.FX)(function(e,t){let n=(0,i.useId)(),r=(0,i.useContext)(eE),{id:o="headlessui-description-".concat(n),...l}=e,a=function e(){let t=(0,i.useContext)(ew);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),u=(0,ev.P)(t);(0,M.s)(()=>a.register(o),[o,a.register]);let s=r||!1,c=(0,i.useMemo)(()=>({...a.slot,disabled:s}),[a.slot,s]),d={ref:u,...a.props,id:o};return(0,ei.Ci)()({ourProps:d,theirProps:l,slot:c,defaultTag:"p",name:a.name||"Description"})}),{});var eP=n(8014),eS=n(3250),eC=n(7856);function eA(e){let t=(0,_._)(e),n=(0,i.useRef)(!1);(0,i.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,eC._)(()=>{n.current&&t()})}),[t])}var ek=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(ek||{});function eO(e,t){let n=(0,i.useRef)([]),r=(0,_._)(e);(0,i.useEffect)(()=>{let e=[...n.current];for(let[o,l]of t.entries())if(n.current[o]!==l){let o=r(t,e);return n.current=t,o}},[r,...t])}let ex=[];function eT(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)X(n.current)&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!K(e.target)||e.target===document.body||ex[0]===e.target)return;let t=e.target;t=t.closest(B),ex.unshift(null!=t?t:e.target),(ex=ex.filter(e=>null!=e&&e.isConnected)).splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var eR=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(eR||{});let e_=Object.assign((0,ei.FX)(function(e,t){let n,r=(0,i.useRef)(null),o=(0,ev.P)(r,t),{initialFocus:l,initialFocusFallback:a,containers:u,features:c=15,...d}=e;(0,ep.g)()||(c=0);let f=el(r);!function(e,t){let{ownerDocument:n}=t,r=!!(8&e),o=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,i.useRef)(ex.slice());return eO((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&(0,eC._)(()=>{t.current.splice(0)}),!1===o&&!0===r&&(t.current=ex.slice())},[e,ex,t]),(0,_._)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);eO(()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&Q(o())},[r]),eA(()=>{r&&Q(o())})}(c,{ownerDocument:f});let p=function(e,t){let{ownerDocument:n,container:r,initialFocus:o,initialFocusFallback:l}=t,a=(0,i.useRef)(null),u=D(!!(1&e),"focus-trap#initial-focus"),s=(0,eS.a)();return eO(()=>{if(0===e)return;if(!u){null!=l&&l.current&&Q(l.current);return}let t=r.current;t&&(0,eC._)(()=>{if(!s.current)return;let r=null==n?void 0:n.activeElement;if(null!=o&&o.current){if((null==o?void 0:o.current)===r){a.current=r;return}}else if(t.contains(r)){a.current=r;return}if(null!=o&&o.current)Q(o.current);else{if(16&e){if(ee(t,G.First|G.AutoFocus)!==z.Error)return}else if(ee(t,G.First)!==z.Error)return;if(null!=l&&l.current&&(Q(l.current),(null==n?void 0:n.activeElement)===l.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}a.current=null==n?void 0:n.activeElement})},[l,u,e]),a}(c,{ownerDocument:f,container:r,initialFocus:l,initialFocusFallback:a});!function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:l}=t,i=(0,eS.a)(),a=!!(4&e);s(null==n?void 0:n.defaultView,"focus",e=>{if(!a||!i.current)return;let t=eT(o);V(r.current)&&t.add(r.current);let n=l.current;if(!n)return;let u=e.target;V(u)?eN(t,u)?(l.current=u,Q(u)):(e.preventDefault(),e.stopPropagation(),Q(n)):Q(l.current)},!0)}(c,{ownerDocument:f,container:r,containers:u,previousActiveElement:p});let v=(n=(0,i.useRef)(0),eo(!0,"keydown",e=>{"Tab"===e.key&&(n.current=+!!e.shiftKey)},!0),n),h=(0,_._)(e=>{if(!V(r.current))return;let t=r.current;(0,P.Y)(v.current,{[ek.Forwards]:()=>{ee(t,G.First,{skipElements:[e.relatedTarget,a]})},[ek.Backwards]:()=>{ee(t,G.Last,{skipElements:[e.relatedTarget,a]})}})}),m=D(!!(2&c),"focus-trap#tab-lock"),g=(0,eP.L)(),b=(0,i.useRef)(!1),y=(0,ei.Ci)();return i.createElement(i.Fragment,null,m&&i.createElement(eu,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:ea.Focusable}),y({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(b.current=!0,g.requestAnimationFrame(()=>{b.current=!1}))},onBlur(e){if(!(4&c))return;let t=eT(u);V(r.current)&&t.add(r.current);let n=e.relatedTarget;K(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(eN(t,n)||(b.current?ee(r.current,(0,P.Y)(v.current,{[ek.Forwards]:()=>G.Next,[ek.Backwards]:()=>G.Previous})|G.WrapAround,{relativeTo:e.target}):K(e.target)&&Q(e.target)))}},theirProps:d,defaultTag:"div",name:"FocusTrap"}),m&&i.createElement(eu,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:ea.Focusable}))}),{features:eR});function eN(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var eL=n(7650);let eM=i.Fragment,eD=(0,ei.FX)(function(e,t){let{ownerDocument:n=null,...r}=e,o=(0,i.useRef)(null),l=(0,ev.P)((0,ev.a)(e=>{o.current=e}),t),a=el(o),u=null!=n?n:a,s=function(e){let t=(0,i.useContext)(eb),n=(0,i.useContext)(eI),[r,o]=(0,i.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(j._.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)});return(0,i.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,i.useEffect)(()=>{t||null!==n&&o(n.current)},[n,o,t]),r}(u),[c]=(0,i.useState)(()=>{var e;return j._.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),d=(0,i.useContext)(eU),f=(0,ep.g)();(0,M.s)(()=>{!s||!c||s.contains(c)||(c.setAttribute("data-headlessui-portal",""),s.appendChild(c))},[s,c]),(0,M.s)(()=>{if(c&&d)return d.register(c)},[d,c]),eA(()=>{var e;s&&c&&(W(c)&&s.contains(c)&&s.removeChild(c),s.childNodes.length<=0&&(null==(e=s.parentElement)||e.removeChild(s)))});let p=(0,ei.Ci)();return f&&s&&c?(0,eL.createPortal)(p({ourProps:{ref:l},theirProps:r,slot:{},defaultTag:eM,name:"Portal"}),c):null}),ej=i.Fragment,eI=(0,i.createContext)(null),eU=(0,i.createContext)(null),eY=(0,ei.FX)(function(e,t){let n=(0,ev.P)(t),{enabled:r=!0,ownerDocument:o,...l}=e,a=(0,ei.Ci)();return r?i.createElement(eD,{...l,ownerDocument:o,ref:n}):a({ourProps:{ref:n},theirProps:l,slot:{},defaultTag:eM,name:"Portal"})}),eH=(0,ei.FX)(function(e,t){let{target:n,...r}=e,o={ref:(0,ev.P)(t)},l=(0,ei.Ci)();return i.createElement(eI.Provider,{value:n},l({ourProps:o,theirProps:r,defaultTag:ej,name:"Popover.Group"}))}),eW=Object.assign(eY,{Group:eH});var eX=n(5939),eV=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(eV||{}),eK=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(eK||{});let eB={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eq=(0,i.createContext)(null);function eG(e){let t=(0,i.useContext)(eq);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,eG),t}return t}function ez(e,t){return(0,P.Y)(t.type,eB,e,t)}eq.displayName="DialogContext";let e$=(0,ei.FX)(function(e,t){let n,r,o,l,c,f,p,v,h,m,g=(0,i.useId)(),{id:b="headlessui-dialog-".concat(g),open:y,onClose:E,initialFocus:w,role:F="dialog",autoFocus:S=!0,__demoMode:C=!1,unmount:A=!1,...k}=e,O=(0,i.useRef)(!1);F="dialog"===F||"alertdialog"===F?F:(O.current||(O.current=!0,console.warn("Invalid role [".concat(F,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let x=(0,eg.O_)();void 0===y&&null!==x&&(y=(x&eg.Uw.Open)===eg.Uw.Open);let R=(0,i.useRef)(null),L=(0,ev.P)(R,t),j=el(R),U=+!y,[Y,W]=(0,i.useReducer)(ez,{titleId:null,descriptionId:null,panelRef:(0,i.createRef)()}),q=(0,_._)(()=>E(!1)),G=(0,_._)(e=>W({type:0,id:e})),z=!!(0,ep.g)()&&0===U,[$,J]=(n=(0,i.useContext)(eU),r=(0,i.useRef)([]),o=(0,_._)(e=>(r.current.push(e),n&&n.register(e),()=>l(e))),l=(0,_._)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),c=(0,i.useMemo)(()=>({register:o,unregister:l,portals:r}),[o,l,r]),[r,(0,i.useMemo)(()=>function(e){let{children:t}=e;return i.createElement(eU.Provider,{value:c},t)},[c])]),Q=ed(),{resolveContainers:ee}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=el(n),o=(0,_._)(()=>{var o,l;let i=[];for(let t of e)null!==t&&(X(t)?i.push(t):"current"in t&&X(t.current)&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&X(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:o,contains:(0,_._)(e=>o().some(t=>t.contains(e)))}}({mainTreeNode:Q,portals:$,defaultContainers:[{get current(){var et;return null!=(et=Y.panelRef.current)?et:R.current}}]}),ea=null!==x&&(x&eg.Uw.Closing)===eg.Uw.Closing;!function(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=D(e,"inert-others");(0,M.s)(()=>{var e,o;if(!r)return;let l=(0,d.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(H(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let e of i){if(!e)continue;let t=I(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||l.add(H(e));n=n.parentElement}}return l.dispose},[r,t,n])}(!C&&!ea&&z,{allowed:(0,_._)(()=>{var e,t;return[null!=(t=null==(e=R.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,_._)(()=>{var e;return[null!=(e=null==Q?void 0:Q.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let eu=T.get(null);(0,M.s)(()=>{if(z)return eu.actions.push(b),()=>eu.actions.pop(b)},[eu,b,z]);let es=N(eu,(0,i.useCallback)(e=>eu.selectors.isTop(e,b),[eu,b]));f=(0,u.Y)(e=>{e.preventDefault(),q()}),p=(0,i.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(ee))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=I(e))?void 0:t.body)&&(0,P.Y)(n,{0:()=>e.matches(B),1(){let t=e;for(;null!==t;){if(t.matches(B))return!0;t=t.parentElement}return!1}})}(n,Z.Loose)||-1===n.tabIndex||e.preventDefault(),f.current(e,n)}},[f,ee]),v=(0,i.useRef)(null),er(es,"pointerdown",e=>{var t,n;en()||(v.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),er(es,"pointerup",e=>{if(en()||!v.current)return;let t=v.current;return v.current=null,p(e,()=>t)},!0),h=(0,i.useRef)({x:0,y:0}),er(es,"touchstart",e=>{h.current.x=e.touches[0].clientX,h.current.y=e.touches[0].clientY},!0),er(es,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-h.current.x)>=30||Math.abs(t.y-h.current.y)>=30))return p(e,()=>K(e.target)?e.target:null)},!0),eo(es,"blur",e=>p(e,()=>{var e;return V(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}),!0),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=D(e,"escape");s(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===a.Escape&&n(e))})}(es,null==j?void 0:j.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),q()}),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=(0,i.useSyncExternalStore)(ef.subscribe,ef.getSnapshot,ef.getSnapshot),o=t?r.get(t):void 0;o&&o.count,(0,M.s)(()=>{if(!(!t||!e))return ef.dispatch("PUSH",t,n),()=>ef.dispatch("POP",t,n)},[e,t])}(D(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!C&&!ea&&z,j,ee),m=(0,u.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&q()}),(0,i.useEffect)(()=>{if(!z)return;let e=null===R?null:V(R)?R:R.current;if(!e)return;let t=(0,d.e)();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>m.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>m.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[R,m,z]);let[ec,eh]=function(){let[e,t]=(0,i.useState)([]);return[e.length>0?e.join(" "):void 0,(0,i.useMemo)(()=>function(e){let n=(0,_._)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,i.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return i.createElement(ew.Provider,{value:r},e.children)},[t])]}(),eb=(0,i.useMemo)(()=>[{dialogState:U,close:q,setTitleId:G,unmount:A},Y],[U,Y,q,G,A]),eE=(0,i.useMemo)(()=>({open:0===U}),[U]),eF={ref:L,id:b,role:F,tabIndex:-1,"aria-modal":C?void 0:0===U||void 0,"aria-labelledby":Y.titleId,"aria-describedby":ec,unmount:A},eP=!function(){var e;let[t]=(0,i.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,i.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,M.s)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),eS=eR.None;z&&!C&&(eS|=eR.RestoreFocus,eS|=eR.TabLock,S&&(eS|=eR.AutoFocus),eP&&(eS|=eR.InitialFocus));let eC=(0,ei.Ci)();return i.createElement(eg.$x,null,i.createElement(ey,{force:!0},i.createElement(eW,null,i.createElement(eq.Provider,{value:eb},i.createElement(eH,{target:R},i.createElement(ey,{force:!1},i.createElement(eh,{slot:eE},i.createElement(J,null,i.createElement(e_,{initialFocus:w,initialFocusFallback:R,containers:ee,features:eS},i.createElement(em,{value:q},eC({ourProps:eF,theirProps:k,slot:eE,defaultTag:eZ,features:eJ,visible:0===U,name:"Dialog"})))))))))))}),eZ="div",eJ=ei.Ac.RenderStrategy|ei.Ac.Static,eQ=Object.assign((0,ei.FX)(function(e,t){let{transition:n=!1,open:r,...o}=e,l=(0,eg.O_)(),a=e.hasOwnProperty("open")||null!==l,u=e.hasOwnProperty("onClose");if(!a&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!l&&"boolean"!=typeof e.open)throw Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return(void 0!==r||n)&&!o.static?i.createElement(ec,null,i.createElement(eX.e,{show:r,transition:n,unmount:o.unmount},i.createElement(e$,{ref:t,...o}))):i.createElement(ec,null,i.createElement(e$,{ref:t,open:r,...o}))}),{Panel:(0,ei.FX)(function(e,t){let n=(0,i.useId)(),{id:r="headlessui-dialog-panel-".concat(n),transition:o=!1,...l}=e,[{dialogState:a,unmount:u},s]=eG("Dialog.Panel"),c=(0,ev.P)(t,s.panelRef),d=(0,i.useMemo)(()=>({open:0===a}),[a]),f=(0,_._)(e=>{e.stopPropagation()}),p=o?eX._:i.Fragment,v=(0,ei.Ci)();return i.createElement(p,{...o?{unmount:u}:{}},v({ourProps:{ref:c,id:r,onClick:f},theirProps:l,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,ei.FX)(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:o,unmount:l}]=eG("Dialog.Backdrop"),a=(0,i.useMemo)(()=>({open:0===o}),[o]),u=n?eX._:i.Fragment,s=(0,ei.Ci)();return i.createElement(u,{...n?{unmount:l}:{}},s({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:a,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,ei.FX)(function(e,t){let n=(0,i.useId)(),{id:r="headlessui-dialog-title-".concat(n),...o}=e,[{dialogState:l,setTitleId:a}]=eG("Dialog.Title"),u=(0,ev.P)(t);(0,i.useEffect)(()=>(a(r),()=>a(null)),[r,a]);let s=(0,i.useMemo)(()=>({open:0===l}),[l]);return(0,ei.Ci)()({ourProps:{ref:u,id:r},theirProps:o,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),Description:eF})},379:(e,t,n)=>{n.d(t,{x:()=>r});function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},797:(e,t,n)=>{n.d(t,{_:()=>l});var r=n(2115),o=n(6232);let l=function(e){let t=(0,o.Y)(e);return r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])}},1231:(e,t,n)=>{n.d(t,{s:()=>l});var r=n(2115),o=n(7657);let l=(e,t)=>{o._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},1525:(e,t,n)=>{n.d(t,{$x:()=>u,El:()=>a,O_:()=>i,Uw:()=>l});var r=n(2115);let o=(0,r.createContext)(null);o.displayName="OpenClosedContext";var l=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(l||{});function i(){return(0,r.useContext)(o)}function a(e){let{value:t,children:n}=e;return r.createElement(o.Provider,{value:t},n)}function u(e){let{children:t}=e;return r.createElement(o.Provider,{value:null},t)}},1992:(e,t,n)=>{e.exports=n(4993)},2525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3250:(e,t,n)=>{n.d(t,{a:()=>l});var r=n(2115),o=n(1231);function l(){let e=(0,r.useRef)(!1);return(0,o.s)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},4554:(e,t,n)=>{n.d(t,{Ac:()=>i,Ci:()=>u,FX:()=>f,mK:()=>a,oE:()=>p});var r=n(2115),o=n(379),l=n(7279),i=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(i||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function u(){let e,t,n=(e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every(e=>null==e))return e.current=r,t});return(0,r.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:i,visible:a=!0,name:u,mergeRefs:f}=e;f=null!=f?f:c;let p=d(n,t);if(a)return s(p,r,o,u,f);let v=null!=i?i:0;if(2&v){let{static:e=!1,...t}=p;if(e)return s(t,r,o,u,f)}if(1&v){let{unmount:e=!0,...t}=p;return(0,l.Y)(+!e,{0:()=>null,1:()=>s({...t,hidden:!0,style:{display:"none"}},r,o,u,f)})}return s(p,r,o,u,f)})({mergeRefs:n,...e}),[n])}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,l=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,{as:a=n,children:u,refName:s="ref",...c}=v(e,["unmount","static"]),f=void 0!==e.ref?{[s]:e.ref}:{},h="function"==typeof u?u(t):u;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let m={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(m["data-headlessui-state"]=n.join(" "),n))m["data-".concat(e)]=""}if(a===r.Fragment&&(Object.keys(p(c)).length>0||Object.keys(p(m)).length>0))if(!(0,r.isValidElement)(h)||Array.isArray(h)&&h.length>1){if(Object.keys(p(c)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(l,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(p(c)).concat(Object.keys(p(m))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var g;let e=h.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,o.x)(t(...n),c.className)}:(0,o.x)(t,c.className),l=d(h.props,p(v(c,["ref"])));for(let e in m)e in l&&delete m[e];return(0,r.cloneElement)(h,Object.assign({},l,m,f,{ref:i((g=h,r.version.split(".")[0]>="19"?g.props.ref:g.ref),f.ref)},n?{className:n}:{}))}return(0,r.createElement)(a,Object.assign({},v(c,["ref"]),a!==r.Fragment&&f,a!==r.Fragment&&m),h)}function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function d(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(o[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in o)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];for(let n of o[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function f(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function p(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function v(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},4616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4993:(e,t,n)=>{var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useSyncExternalStore,i=r.useRef,a=r.useEffect,u=r.useMemo,s=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=l(e,(d=u(function(){function e(e){if(!a){if(a=!0,l=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return i=t}return i=e}if(t=i,o(l,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(l=e,t):(l=e,i=n)}var l,i,a=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,c]))[0],d[1]);return a(function(){f.hasValue=!0,f.value=p},[p]),s(p),p}},5261:(e,t,n)=>{n.d(t,{e:()=>function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];let l={current:!0};return(0,r._)(()=>{l.current&&t[0]()}),n.add(()=>{l.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(7856)},5939:(e,t,n)=>{n.d(t,{e:()=>R,_:()=>T});var r,o,l=n(2115),i=n(8014),a=n(797),u=n(3250),s=n(1231),c=n(6232),d=n(9925),f=n(7769),p=n(5261),v=n(9509);void 0!==v&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==v?void 0:v.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(o=null==Element?void 0:Element.prototype)?void 0:o.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var h=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(h||{}),m=n(1525),g=n(379),b=n(7279),y=n(4554);function E(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:A)!==l.Fragment||1===l.Children.count(e.children)}let w=(0,l.createContext)(null);w.displayName="TransitionContext";var F=(e=>(e.Visible="visible",e.Hidden="hidden",e))(F||{});let P=(0,l.createContext)(null);function S(e){return"children"in e?S(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function C(e,t){let n=(0,c.Y)(e),r=(0,l.useRef)([]),o=(0,u.a)(),s=(0,i.L)(),d=(0,a._)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:y.mK.Hidden,l=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==l&&((0,b.Y)(t,{[y.mK.Unmount](){r.current.splice(l,1)},[y.mK.Hidden](){r.current[l].state="hidden"}}),s.microTask(()=>{var e;!S(r)&&o.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,a._)(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,y.mK.Unmount)}),p=(0,l.useRef)([]),v=(0,l.useRef)(Promise.resolve()),h=(0,l.useRef)({enter:[],leave:[]}),m=(0,a._)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(h.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?v.current=v.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),g=(0,a._)((e,t,n)=>{Promise.all(h.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,l.useMemo)(()=>({children:r,register:f,unregister:d,onStart:m,onStop:g,wait:v,chains:h}),[f,d,r,m,g,h,v])}P.displayName="NestingContext";let A=l.Fragment,k=y.Ac.RenderStrategy,O=(0,y.FX)(function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...i}=e,u=(0,l.useRef)(null),c=E(e),p=(0,f.P)(...c?[u,t]:null===t?[]:[t]);(0,d.g)();let v=(0,m.O_)();if(void 0===n&&null!==v&&(n=(v&m.Uw.Open)===m.Uw.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[h,g]=(0,l.useState)(n?"visible":"hidden"),b=C(()=>{n||g("hidden")}),[F,A]=(0,l.useState)(!0),O=(0,l.useRef)([n]);(0,s.s)(()=>{!1!==F&&O.current[O.current.length-1]!==n&&(O.current.push(n),A(!1))},[O,n]);let T=(0,l.useMemo)(()=>({show:n,appear:r,initial:F}),[n,r,F]);(0,s.s)(()=>{n?g("visible"):S(b)||null===u.current||g("hidden")},[n,b]);let R={unmount:o},_=(0,a._)(()=>{var t;F&&A(!1),null==(t=e.beforeEnter)||t.call(e)}),N=(0,a._)(()=>{var t;F&&A(!1),null==(t=e.beforeLeave)||t.call(e)}),L=(0,y.Ci)();return l.createElement(P.Provider,{value:b},l.createElement(w.Provider,{value:T},L({ourProps:{...R,as:l.Fragment,children:l.createElement(x,{ref:p,...R,...i,beforeEnter:_,beforeLeave:N})},theirProps:{},defaultTag:l.Fragment,features:k,visible:"visible"===h,name:"Transition"})))}),x=(0,y.FX)(function(e,t){var n,r;let{transition:o=!0,beforeEnter:u,afterEnter:c,beforeLeave:v,afterLeave:h,enter:F,enterFrom:O,enterTo:x,entered:T,leave:R,leaveFrom:_,leaveTo:N,...L}=e,[M,D]=(0,l.useState)(null),j=(0,l.useRef)(null),I=E(e),U=(0,f.P)(...I?[j,t,D]:null===t?[]:[t]),Y=null==(n=L.unmount)||n?y.mK.Unmount:y.mK.Hidden,{show:H,appear:W,initial:X}=function(){let e=(0,l.useContext)(w);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[V,K]=(0,l.useState)(H?"visible":"hidden"),B=function(){let e=(0,l.useContext)(P);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:q,unregister:G}=B;(0,s.s)(()=>q(j),[q,j]),(0,s.s)(()=>{if(Y===y.mK.Hidden&&j.current)return H&&"visible"!==V?void K("visible"):(0,b.Y)(V,{hidden:()=>G(j),visible:()=>q(j)})},[V,j,q,G,H,Y]);let z=(0,d.g)();(0,s.s)(()=>{if(I&&z&&"visible"===V&&null===j.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[j,V,z,I]);let $=X&&!W,Z=W&&H&&X,J=(0,l.useRef)(!1),Q=C(()=>{J.current||(K("hidden"),G(j))},B),ee=(0,a._)(e=>{J.current=!0,Q.onStart(j,e?"enter":"leave",e=>{"enter"===e?null==u||u():"leave"===e&&(null==v||v())})}),et=(0,a._)(e=>{let t=e?"enter":"leave";J.current=!1,Q.onStop(j,t,e=>{"enter"===e?null==c||c():"leave"===e&&(null==h||h())}),"leave"!==t||S(Q)||(K("hidden"),G(j))});(0,l.useEffect)(()=>{I&&o||(ee(H),et(H))},[H,I,o]);let[,en]=function(e,t,n,r){let[o,a]=(0,l.useState)(n),{hasFlag:u,addFlag:c,removeFlag:d}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,l.useState)(e),r=(0,l.useCallback)(e=>n(e),[t]),o=(0,l.useCallback)(e=>n(t=>t|e),[t]),i=(0,l.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:(0,l.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,l.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),f=(0,l.useRef)(!1),v=(0,l.useRef)(!1),h=(0,i.L)();return(0,s.s)(()=>{var o;if(e){if(n&&a(!0),!t){n&&c(3);return}return null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,t){let{prepare:n,run:r,done:o,inFlight:l}=t,i=(0,p.e)();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let o=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=o}(e,{prepare:n,inFlight:l}),i.nextFrame(()=>{r(),i.requestAnimationFrame(()=>{i.add(function(e,t){var n,r;let o=(0,p.e)();if(!e)return o.dispose;let l=!1;o.add(()=>{l=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?t():Promise.allSettled(i.map(e=>e.finished)).then(()=>{l||t()}),o.dispose}(e,o))})}),i.dispose}(t,{inFlight:f,prepare(){v.current?v.current=!1:v.current=f.current,f.current=!0,v.current||(n?(c(3),d(4)):(c(4),d(2)))},run(){v.current?n?(d(3),c(4)):(d(4),c(3)):n?d(1):c(1)},done(){var e;v.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,d(7),n||a(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,h]),e?[o,{closed:u(1),enter:u(2),leave:u(4),transition:u(2)||u(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!o||!I||!z||$),M,H,{start:ee,end:et}),er=(0,y.oE)({ref:U,className:(null==(r=(0,g.x)(L.className,Z&&F,Z&&O,en.enter&&F,en.enter&&en.closed&&O,en.enter&&!en.closed&&x,en.leave&&R,en.leave&&!en.closed&&_,en.leave&&en.closed&&N,!en.transition&&H&&T))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(en)}),eo=0;"visible"===V&&(eo|=m.Uw.Open),"hidden"===V&&(eo|=m.Uw.Closed),H&&"hidden"===V&&(eo|=m.Uw.Opening),H||"visible"!==V||(eo|=m.Uw.Closing);let el=(0,y.Ci)();return l.createElement(P.Provider,{value:Q},l.createElement(m.El,{value:eo},el({ourProps:er,theirProps:L,defaultTag:A,features:k,visible:"visible"===V,name:"Transition.Child"})))}),T=(0,y.FX)(function(e,t){let n=null!==(0,l.useContext)(w),r=null!==(0,m.O_)();return l.createElement(l.Fragment,null,!n&&r?l.createElement(O,{ref:t,...e}):l.createElement(x,{ref:t,...e}))}),R=Object.assign(O,{Child:T,Root:O})},6232:(e,t,n)=>{n.d(t,{Y:()=>l});var r=n(2115),o=n(1231);function l(e){let t=(0,r.useRef)(e);return(0,o.s)(()=>{t.current=e},[e]),t}},7279:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e,t){for(var n=arguments.length,o=Array(n>2?n-2:0),l=2;l<n;l++)o[l-2]=arguments[l];if(e in t){let n=t[e];return"function"==typeof n?n(...o):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,r),i}},7657:(e,t,n)=>{n.d(t,{_:()=>a});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}}let a=new i},7769:(e,t,n)=>{n.d(t,{P:()=>a,a:()=>i});var r=n(2115),o=n(797);let l=Symbol();function i(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[l]:t})}function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=(0,r.useRef)(t);(0,r.useEffect)(()=>{i.current=t},[t]);let a=(0,o._)(e=>{for(let t of i.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[l]))?void 0:a}},7856:(e,t,n)=>{n.d(t,{_:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},8014:(e,t,n)=>{n.d(t,{L:()=>l});var r=n(2115),o=n(5261);function l(){let[e]=(0,r.useState)(o.e);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},9925:(e,t,n)=>{n.d(t,{g:()=>i});var r,o=n(2115),l=n(7657);function i(){let e,t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,a]=o.useState(l._.isHandoffComplete);return i&&!1===l._.isHandoffComplete&&a(!1),o.useEffect(()=>{!0!==i&&a(!0)},[i]),o.useEffect(()=>l._.handoff(),[]),!t&&i}}}]);