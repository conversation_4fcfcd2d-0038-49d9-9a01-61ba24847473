"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[809],{283:(e,t,r)=>{r.d(t,{A:()=>o,AuthProvider:()=>i});var a=r(5155),n=r(2115),s=r(9959);let l=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,n.useState)(null),[o,c]=(0,n.useState)(null),[u,d]=(0,n.useState)(null),[h,g]=(0,n.useState)(!0);(0,n.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("auth_user"),r=localStorage.getItem("auth_organization");if(e&&t)try{let a=JSON.parse(t);if(d(e),i(a),r){let e=JSON.parse(r);c(e)}}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}g(!1)},[]);let m=async e=>{try{let t=await s.y.login(e);d(t.token),i(t.user),c(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},y={user:r,organization:o,token:u,isLoading:h,isAuthenticated:!!r&&!!u,isAdmin:(null==r?void 0:r.role)==="admin",login:m,logout:()=>{d(null),i(null),c(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,a.jsx)(l.Provider,{value:y,children:t})}function o(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1007:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1586:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2713:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3062:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("megaphone",[["path",{d:"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z",key:"q8bfy3"}],["path",{d:"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14",key:"1853fq"}],["path",{d:"M8 6v8",key:"15ugcq"}]])},3332:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3741:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(5155),n=r(2115),s=r(2596);let l=(0,n.forwardRef)((e,t)=>{let{className:r,variant:n="primary",size:l="md",loading:i,children:o,disabled:c,...u}=e;return(0,a.jsxs)("button",{className:(0,s.A)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500":"primary"===n,"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500":"secondary"===n,"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500":"danger"===n,"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500":"success"===n,"bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500":"warning"===n,"bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500":"info"===n,"px-3 py-1.5 text-sm":"sm"===l,"px-4 py-2 text-sm":"md"===l,"px-6 py-3 text-base":"lg"===l},r),disabled:c||i,ref:t,...u,children:[i&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});l.displayName="Button";let i=l},5731:(e,t,r)=>{r.d(t,{u:()=>s});let a=r(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:5555/api/v1";class n{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseURL).concat(e),a=localStorage.getItem("auth_token"),n={headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)},...t.headers},...t};try{let e=await fetch(r,n);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP error! status: ".concat(e.status))}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let r=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[r,a]=t;null!=a&&e.append(r,String(a))});let a=e.toString();a&&(r+="?".concat(a))}return this.request(r,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}constructor(e){this.baseURL=e}}let s=new n(a)},5809:(e,t,r)=>{r.d(t,{A:()=>M});var a=r(5155),n=r(6874),s=r.n(n),l=r(5695),i=r(7340),o=r(7108),c=r(7809),u=r(7580),d=r(1586),h=r(8048),g=r(3332),m=r(3062),y=r(2713),x=r(4416),p=r(4783),f=r(2115),v=r(2596);let A=[{name:"Dashboard",href:"/",icon:i.A},{name:"\xdcr\xfcnler",href:"/products",icon:o.A},{name:"Satışlar",href:"/sales",icon:c.A},{name:"M\xfcşteriler",href:"/customers",icon:u.A},{name:"Bor\xe7lar",href:"/debts",icon:d.A},{name:"Kasa",href:"/safe",icon:h.A},{name:"Kategoriler",href:"/categories",icon:g.A},{name:"Kampanyalar",href:"/campaigns",icon:m.A},{name:"Raporlar",href:"/reports",icon:y.A}];function b(){let e=(0,l.usePathname)(),[t,r]=(0,f.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"lg:hidden fixed top-4 left-4 z-50",children:(0,a.jsx)("button",{onClick:()=>r(!t),className:"p-2 rounded-md bg-white shadow-md border border-gray-200",children:t?(0,a.jsx)(x.A,{className:"h-6 w-6 text-gray-600"}):(0,a.jsx)(p.A,{className:"h-6 w-6 text-gray-600"})})}),t&&(0,a.jsx)("div",{className:"lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50",onClick:()=>r(!1)}),(0,a.jsx)("div",{className:(0,v.A)("fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",t?"translate-x-0":"-translate-x-full"),children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"flex items-center justify-center h-16 px-4 border-b border-gray-200",children:(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Business Manager"})}),(0,a.jsx)("nav",{className:"flex-1 px-4 py-6 space-y-2",children:A.map(t=>{let n=e===t.href;return(0,a.jsxs)(s(),{href:t.href,onClick:()=>r(!1),className:(0,v.A)("flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors",n?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"),children:[(0,a.jsx)(t.icon,{className:(0,v.A)("mr-3 h-5 w-5",n?"text-blue-700":"text-gray-400")}),t.name]},t.name)})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,a.jsx)("div",{className:"text-xs text-gray-500 text-center",children:"Business Management v1.0"})})]})})]})}var k=r(283),w=r(1007);let j=(0,r(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var N=r(4835),S=r(3741);function _(){let e=(0,l.useRouter)(),{user:t,logout:r}=(0,k.A)();return(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-end items-center h-16",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(w.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:null==t?void 0:t.username})]}),(0,a.jsxs)("button",{className:"p-2 text-gray-400 hover:text-gray-500 relative",children:[(0,a.jsx)(j,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"})]}),(0,a.jsxs)(S.A,{onClick:()=>{r(),e.push("/login")},variant:"secondary",size:"sm",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"\xc7ıkış"]})]})})})})}var I=r(6759);function z(){let[e,t]=(0,f.useState)(!1);return(0,I.A1)(),null}function M(e){let{children:t}=e;return(0,a.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,a.jsx)(b,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden lg:ml-0",children:[(0,a.jsx)(_,{}),(0,a.jsx)("main",{className:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:t})})]}),(0,a.jsx)(z,{})]})}},6759:(e,t,r)=>{r.d(t,{A1:()=>s,jp:()=>l});var a=r(283),n=r(2115);function s(){let{user:e,token:t,isAuthenticated:r,isAdmin:s}=(0,a.A)();return(0,n.useMemo)(()=>{var a,n,l;if(!e||!t)return{currentUserID:null,currentUserOrganizationID:null,currentAdminID:null,currentAdminToken:null,currentDepartmentID:null,isMainOrg:!1,isAuthorized:!1,currentUserIP:null,currentAdminAgent:null,apiKey:null,apiSecret:null,internalAuth:!1,isAdmin:!1,isAuthenticated:!1};let i={};try{let e=t.split(".")[1],r=atob(e);i=JSON.parse(r)}catch(e){console.error("Error parsing token:",e)}return{currentUserID:e.id,currentUserOrganizationID:e.organization_id,currentAdminID:i.admin_id||e.id,currentAdminToken:i.admin_token||null,currentDepartmentID:i.department_id||null,isMainOrg:null==(a=i.is_main_org)||a,isAuthorized:null==(n=i.is_authorized)||n,currentUserIP:i.user_ip||null,currentAdminAgent:i.admin_agent||null,apiKey:i.api_key||null,apiSecret:i.api_secret||null,internalAuth:null!=(l=i.internal_auth)&&l,isAdmin:s,isAuthenticated:r}},[e,t,r,s])}let l=()=>s().currentUserOrganizationID},7108:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7809:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},8048:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("vault",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}],["path",{d:"m7.9 7.9 2.7 2.7",key:"hpeyl3"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}],["path",{d:"m13.4 10.6 2.7-2.7",key:"264c1n"}],["circle",{cx:"7.5",cy:"16.5",r:".5",fill:"currentColor",key:"nkw3mc"}],["path",{d:"m7.9 16.1 2.7-2.7",key:"p81g5e"}],["circle",{cx:"16.5",cy:"16.5",r:".5",fill:"currentColor",key:"fubopw"}],["path",{d:"m13.4 13.4 2.7 2.7",key:"abhel3"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},9959:(e,t,r)=>{r.d(t,{y:()=>n});var a=r(5731);let n={async login(e){let t=await a.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await a.u.post("/users",e)},getAllUsers:async()=>(await a.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await a.u.get("/users/paginated?".concat(t))},async getUserById(e){let t=await a.u.get("/users/".concat(e));if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await a.u.put("/users/".concat(e),t)},async deleteUser(e){await a.u.delete("/users/".concat(e))}}}}]);