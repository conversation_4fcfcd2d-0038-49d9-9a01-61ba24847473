"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[329],{283:(e,t,a)=>{a.d(t,{A:()=>c,AuthProvider:()=>i});var s=a(5155),r=a(2115),n=a(9959);let l=(0,r.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,r.useState)(null),[c,o]=(0,r.useState)(null),[d,h]=(0,r.useState)(null),[m,u]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("auth_user"),a=localStorage.getItem("auth_organization");if(e&&t)try{let s=JSON.parse(t);if(h(e),i(s),a){let e=JSON.parse(a);o(e)}}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}u(!1)},[]);let g=async e=>{try{let t=await n.y.login(e);h(t.token),i(t.user),o(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},x={user:a,organization:c,token:d,isLoading:m,isAuthenticated:!!a&&!!d,isAdmin:(null==a?void 0:a.role)==="admin",login:g,logout:()=>{h(null),i(null),o(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,s.jsx)(l.Provider,{value:x,children:t})}function c(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2752:(e,t,a)=>{a.d(t,{A:()=>p});var s=a(5155),r=a(2115),n=a(6874),l=a.n(n),i=a(5695),c=a(283),o=a(7340);let d=(0,a(9946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);var h=a(7580),m=a(5525),u=a(4416),g=a(4835),x=a(4783),f=a(3052),y=a(3741);function p(e){var t;let{children:a}=e,[n,p]=(0,r.useState)(!1),{user:b,logout:v}=(0,c.A)(),j=(0,i.useRouter)(),N=(0,i.usePathname)(),w=()=>{v(),j.push("/login")},A=[{name:"Dashboard",href:"/admin",icon:o.A},{name:"Organizasyon Y\xf6netimi",href:"/admin/organizations",icon:d},{name:"Kullanıcı Y\xf6netimi",href:"/admin/users",icon:h.A}],k=e=>"/admin"===e?"/admin"===N:N.startsWith(e);return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden ".concat(n?"block":"hidden"),children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>p(!1)}),(0,s.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white",children:[(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Admin Panel"})]}),(0,s.jsx)("button",{onClick:()=>p(!1),children:(0,s.jsx)(u.A,{className:"h-6 w-6 text-gray-400"})})]}),(0,s.jsx)("nav",{className:"flex-1 px-4 py-4",children:(0,s.jsx)("ul",{className:"space-y-2",children:A.map(e=>(0,s.jsx)("li",{children:(0,s.jsxs)(l(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(k(e.href)?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100"),onClick:()=>p(!1),children:[(0,s.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]})},e.name))})})]})]}),(0,s.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,s.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[(0,s.jsxs)("div",{className:"flex h-16 items-center px-4 border-b border-gray-200",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Admin Panel"})]}),(0,s.jsx)("nav",{className:"flex-1 px-4 py-4",children:(0,s.jsx)("ul",{className:"space-y-2",children:A.map(e=>(0,s.jsx)("li",{children:(0,s.jsxs)(l(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(k(e.href)?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100"),children:[(0,s.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]})},e.name))})}),(0,s.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:null==b?void 0:b.username}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Admin"})]})]}),(0,s.jsxs)(y.A,{onClick:w,variant:"secondary",size:"sm",className:"mt-3 w-full",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"\xc7ıkış Yap"]})]})]})}),(0,s.jsxs)("div",{className:"lg:pl-64",children:[(0,s.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("button",{onClick:()=>p(!0),className:"lg:hidden",children:(0,s.jsx)(x.A,{className:"h-6 w-6 text-gray-400"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,s.jsx)(l(),{href:"/admin",className:"hover:text-gray-700",children:"Admin"}),"/admin"!==N&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-gray-900",children:(null==(t=A.find(e=>N.startsWith(e.href)))?void 0:t.name)||"Sayfa"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-700",children:["Hoş geldin, ",null==b?void 0:b.username]}),(0,s.jsx)(y.A,{onClick:w,variant:"secondary",size:"sm",className:"lg:hidden",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)("main",{className:"p-4 sm:p-6 lg:p-8",children:a})]})]})}},3741:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(5155),r=a(2115),n=a(2596);let l=(0,r.forwardRef)((e,t)=>{let{className:a,variant:r="primary",size:l="md",loading:i,children:c,disabled:o,...d}=e;return(0,s.jsxs)("button",{className:(0,n.A)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500":"primary"===r,"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500":"secondary"===r,"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500":"danger"===r,"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500":"success"===r,"bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500":"warning"===r,"bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500":"info"===r,"px-3 py-1.5 text-sm":"sm"===l,"px-4 py-2 text-sm":"md"===l,"px-6 py-3 text-base":"lg"===l},a),disabled:o||i,ref:t,...d,children:[i&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]})});l.displayName="Button";let i=l},5525:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5731:(e,t,a)=>{a.d(t,{u:()=>n});let s=a(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:5555/api/v1";class r{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="".concat(this.baseURL).concat(e),s=localStorage.getItem("auth_token"),r={headers:{"Content-Type":"application/json",...s&&{Authorization:"Bearer ".concat(s)},...t.headers},...t};try{let e=await fetch(a,r);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP error! status: ".concat(e.status))}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let a=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,s]=t;null!=s&&e.append(a,String(s))});let s=e.toString();s&&(a+="?".concat(s))}return this.request(a,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}constructor(e){this.baseURL=e}}let n=new r(s)},5876:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(5155),r=a(2115),n=a(5695),l=a(283);function i(e){let{children:t,requireAdmin:a=!1}=e,{isAuthenticated:i,isAdmin:c,isLoading:o}=(0,l.A)(),d=(0,n.useRouter)();return((0,r.useEffect)(()=>{if(!o){if(!i)return void d.push("/login");if(a&&!c)return void d.push("/")}},[i,c,o,a,d]),o)?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):i&&(!a||c)?(0,s.jsx)(s.Fragment,{children:t}):null}},9959:(e,t,a)=>{a.d(t,{y:()=>r});var s=a(5731);let r={async login(e){let t=await s.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await s.u.post("/users",e)},getAllUsers:async()=>(await s.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/users/paginated?".concat(t))},async getUserById(e){let t=await s.u.get("/users/".concat(e));if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await s.u.put("/users/".concat(e),t)},async deleteUser(e){await s.u.delete("/users/".concat(e))}}}}]);