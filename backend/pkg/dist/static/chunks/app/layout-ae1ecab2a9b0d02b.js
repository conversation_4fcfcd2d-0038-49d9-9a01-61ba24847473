(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,AuthProvider:()=>l});var r=a(5155),o=a(2115),s=a(9959);let n=(0,o.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,o.useState)(null),[i,u]=(0,o.useState)(null),[c,h]=(0,o.useState)(null),[g,d]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("auth_user"),a=localStorage.getItem("auth_organization");if(e&&t)try{let r=JSON.parse(t);if(h(e),l(r),a){let e=JSON.parse(a);u(e)}}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}d(!1)},[]);let m=async e=>{try{let t=await s.y.login(e);h(t.token),l(t.user),u(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},_={user:a,organization:i,token:c,isLoading:g,isAuthenticated:!!a&&!!c,isAdmin:(null==a?void 0:a.role)==="admin",login:m,logout:()=>{h(null),l(null),u(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,r.jsx)(n.Provider,{value:_,children:t})}function i(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5731:(e,t,a)=>{"use strict";a.d(t,{u:()=>o});class r{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="".concat(this.baseURL).concat(e),r=localStorage.getItem("auth_token"),o={headers:{"Content-Type":"application/json",...r&&{Authorization:"Bearer ".concat(r)},...t.headers},...t};try{let e=await fetch(a,o);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP error! status: ".concat(e.status))}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let a=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&e.append(a,String(r))});let r=e.toString();r&&(a+="?".concat(r))}return this.request(a,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}constructor(e){this.baseURL=e}}let o=new r("http://localhost:5555/api/v1")},8116:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,4147,23)),Promise.resolve().then(a.t.bind(a,8489,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,283))},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9959:(e,t,a)=>{"use strict";a.d(t,{y:()=>o});var r=a(5731);let o={async login(e){let t=await r.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await r.u.post("/users",e)},getAllUsers:async()=>(await r.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/users/paginated?".concat(t))},async getUserById(e){let t=await r.u.get("/users/".concat(e));if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await r.u.put("/users/".concat(e),t)},async deleteUser(e){await r.u.delete("/users/".concat(e))}}}},e=>{var t=t=>e(e.s=t);e.O(0,[896,441,684,977],()=>t(8116)),_N_E=e.O()}]);