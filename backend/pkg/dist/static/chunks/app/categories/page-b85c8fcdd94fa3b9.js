(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[379],{2716:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>y});var r=t(5155),s=t(2115),l=t(5809),n=t(3741),i=t(6440),c=t(3915),o=t(542);let d=(0,t(9946).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var g=t(3717),h=t(2525),p=t(4616),x=t(7924),m=t(4957),u=t(283);function y(){let{user:e}=(0,u.A)(),[a,t]=(0,s.useState)([]),[y,j]=(0,s.useState)(!0),[v,f]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),[A,w]=(0,s.useState)(null),[C,_]=(0,s.useState)({name:""}),[b,S]=(0,s.useState)({page:1,per_page:10}),[E,K]=(0,s.useState)(""),[z,P]=(0,s.useState)("");(0,s.useEffect)(()=>{U()},[]);let U=async()=>{try{j(!0);let e=await m.U.getAll();t(e)}catch(e){console.error("Error loading categories:",e)}finally{j(!1)}},O=()=>{K(z),S(e=>({...e,page:1}))},D=e=>{P(e)},L=async()=>{try{let a={...C,organization_id:(null==e?void 0:e.organization_id)||""};await m.U.create(a),f(!1),_({name:""}),U()}catch(e){console.error("Error creating category:",e)}},M=async()=>{if(A)try{let e={name:C.name};await m.U.update(A.id,e),k(!1),w(null),U()}catch(e){console.error("Error updating category:",e)}},T=async e=>{if(confirm("Bu kategoriyi silmek istediğinizden emin misiniz?"))try{await m.U.delete(e),U()}catch(e){console.error("Error deleting category:",e)}},Y=e=>{w(e),_({name:e.name}),k(!0)},B=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),H=a.filter(e=>{if(!E)return!0;let a=B(E);return B(e.name).includes(a)}),I=[{key:"name",header:"Kategori",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name})]})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("tr-TR")})},{key:"actions",header:"İşlemler",render:e=>(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(n.A,{size:"sm",variant:"secondary",onClick:()=>Y(e),children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.A,{size:"sm",variant:"danger",onClick:()=>T(e.id),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})}];return y?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kategoriler"}),(0,r.jsx)("p",{className:"text-gray-600",children:"\xdcr\xfcn kategorilerinizi y\xf6netin"})]}),(0,r.jsxs)(n.A,{onClick:()=>f(!0),children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Yeni Kategori"]})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Kategori adı ara...",value:z,onChange:e=>D(e.target.value),onKeyDown:e=>"Enter"===e.key&&O(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)(n.A,{onClick:O,variant:"primary",className:"px-4 py-2",children:"Ara"}),E&&(0,r.jsx)(n.A,{onClick:()=>{K(""),P(""),S(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),E&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[H.length,' kategori g\xf6steriliyor • "',E,'" araması']})]}),(0,r.jsx)(o.A,{columns:I,data:H,pagination:{page:b.page,per_page:b.per_page,total:H.length,total_pages:Math.ceil(H.length/b.per_page),has_next:b.page<Math.ceil(H.length/b.per_page),has_prev:b.page>1},onPageChange:e=>{S(a=>({...a,page:e}))},onPerPageChange:e=>{S({page:1,per_page:e})},loading:y,emptyMessage:"Hen\xfcz kategori kaydı bulunmuyor",emptyIcon:(0,r.jsx)(d,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0}),(0,r.jsx)(i.A,{isOpen:v,onClose:()=>f(!1),title:"Yeni Kategori Ekle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c.A,{label:"Kategori Adı",value:C.name,onChange:e=>_({...C,name:e.target.value}),placeholder:"Kategori adını girin"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(n.A,{variant:"secondary",onClick:()=>f(!1),children:"İptal"}),(0,r.jsx)(n.A,{onClick:L,children:"Kategori Ekle"})]})]})}),(0,r.jsx)(i.A,{isOpen:N,onClose:()=>k(!1),title:"Kategori D\xfczenle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c.A,{label:"Kategori Adı",value:C.name,onChange:e=>_({...C,name:e.target.value}),placeholder:"Kategori adını girin"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(n.A,{variant:"secondary",onClick:()=>k(!1),children:"İptal"}),(0,r.jsx)(n.A,{onClick:M,children:"G\xfcncelle"})]})]})})]})})}},4957:(e,a,t)=>{"use strict";t.d(a,{U:()=>s});var r=t(5731);let s={getAll:async()=>(await r.u.get("/categories")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/categories/paginated?".concat(a))},async getById(e){try{return(await r.u.get("/categories/".concat(e))).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await r.u.post("/categories",e)},async update(e,a){await r.u.put("/categories/".concat(e),a)},async delete(e){await r.u.delete("/categories/".concat(e))}}},9881:(e,a,t)=>{Promise.resolve().then(t.bind(t,2716))}},e=>{var a=a=>e(e.s=a);e.O(0,[122,269,809,564,441,684,977],()=>a(9881)),_N_E=e.O()}]);