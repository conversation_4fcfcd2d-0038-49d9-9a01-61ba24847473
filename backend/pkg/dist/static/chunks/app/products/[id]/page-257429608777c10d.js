(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[403],{277:(e,a,t)=>{Promise.resolve().then(t.bind(t,1113))},1113:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>b});var s=t(5155),r=t(2115),c=t(5695),n=t(7559),l=t(3741),i=t(2814),d=t(9604),o=t(4957),u=t(4975),m=t(9434),x=t(3717),g=t(2525),p=t(7108),y=t(9946);let h=(0,y.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var j=t(3332),f=t(5868);let v=(0,y.A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);var N=t(3062);function b(){let e=(0,c.useParams)().id,[a,t]=(0,r.useState)(null),[y,b]=(0,r.useState)(null),[w,k]=(0,r.useState)(null),[A,_]=(0,r.useState)(!0),[S,R]=(0,r.useState)(null),C=(0,r.useCallback)(async()=>{try{_(!0),R(null);let a=await d.j.getById(e);if(!a)return void R("\xdcr\xfcn bulunamadı.");if(t(a),a.category_id){let e=await o.U.getById(a.category_id);e&&b(e)}if(a.campaign_id&&"00000000-0000-0000-0000-000000000000"!==a.campaign_id){let e=await u.A.getById(a.campaign_id);e&&k(e)}}catch(e){console.error("Error loading product:",e),R("\xdcr\xfcn bilgileri y\xfcklenirken bir hata oluştu.")}finally{_(!1)}},[e]);(0,r.useEffect)(()=>{C()},[C]);let E=a&&a.discounted_price<a.price,H=a&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,s.jsxs)(l.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,s.jsx)(n.Ay,{title:(null==a?void 0:a.name)||"\xdcr\xfcn Detayı",subtitle:"\xdcr\xfcn bilgilerini g\xf6r\xfcnt\xfcleyin",loading:A,error:S,backUrl:"/products",actions:H,children:a&&(0,s.jsxs)(n.A7,{columns:2,children:[(0,s.jsx)(n.JH,{title:"Genel Bilgiler",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(n.Qn,{label:"\xdcr\xfcn Adı",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"font-medium",children:a.name})]})}),(0,s.jsx)(n.Qn,{label:"\xdcr\xfcn Kodu",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(h,{className:"h-4 w-4 text-gray-600 mr-2"}),(0,s.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-2 py-1 rounded",children:a.product_code})]})}),(0,s.jsx)(n.Qn,{label:"Kategori",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,s.jsx)("span",{children:(null==y?void 0:y.name)||"Kategori bulunamadı"})]})}),(0,s.jsx)(n.Qn,{label:"Stok Durumu",value:a?0===a.quantity?(0,s.jsx)(i.E,{variant:"danger",children:"Stokta Yok"}):a.quantity<=5?(0,s.jsx)(i.E,{variant:"warning",children:"D\xfcş\xfck Stok"}):(0,s.jsx)(i.E,{variant:"success",children:"Stokta Var"}):null})]})}),(0,s.jsx)(n.JH,{title:"Fiyat ve Stok",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(n.Qn,{label:"Orijinal Fiyat",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,s.jsxs)("span",{className:E?"line-through text-gray-500":"font-semibold text-green-600",children:["₺",a.price.toLocaleString("tr-TR")]})]})}),E&&(0,s.jsx)(n.Qn,{label:"İndirimli Fiyat",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,s.jsxs)("span",{className:"font-semibold text-red-600",children:["₺",a.discounted_price.toLocaleString("tr-TR")]})]})}),(0,s.jsx)(n.Qn,{label:"Stok Miktarı",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(v,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,s.jsxs)("span",{className:"font-semibold",children:[a.quantity," adet"]})]})})]})}),w&&(0,s.jsxs)(n.JH,{title:"Kampanya Bilgileri",className:"md:col-span-2",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsx)(n.Qn,{label:"Kampanya Adı",value:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 text-orange-600 mr-2"}),(0,s.jsx)("span",{className:"font-medium",children:w.name})]})}),(0,s.jsx)(n.Qn,{label:"İndirim T\xfcr\xfc",value:"percentage"===w.discount_type?"Y\xfczde":"Sabit Tutar"}),(0,s.jsx)(n.Qn,{label:"İndirim Miktarı",value:"percentage"===w.discount_type?"%".concat(w.discount_percent):"₺".concat(w.discount_amount.toLocaleString("tr-TR"))})]}),E&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,s.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,s.jsx)("strong",{children:"Kampanya Aktif:"}),' Bu \xfcr\xfcn şu anda "',w.name,'" kampanyası kapsamında indirimli satılmaktadır.']})})]}),(0,s.jsx)(n.JH,{title:"Tarih Bilgileri",className:w?"":"md:col-span-2",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(n.Qn,{label:"Oluşturulma",value:(0,m.Yq)(a.created_at)}),(0,s.jsx)(n.Qn,{label:"Son G\xfcncelleme",value:(0,m.Yq)(a.updated_at)})]})})]})})}},2525:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2814:(e,a,t)=>{"use strict";t.d(a,{E:()=>r});var s=t(5155);function r(e){let{children:a,variant:t="default",className:r=""}=e;return(0,s.jsx)("span",{className:"".concat("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"," ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",danger:"bg-red-100 text-red-800",warning:"bg-yellow-100 text-yellow-800",info:"bg-blue-100 text-blue-800",secondary:"bg-gray-100 text-gray-600"}[t]," ").concat(r),children:a})}t(2115)},3717:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4957:(e,a,t)=>{"use strict";t.d(a,{U:()=>r});var s=t(5731);let r={getAll:async()=>(await s.u.get("/categories")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/categories/paginated?".concat(a))},async getById(e){try{return(await s.u.get("/categories/".concat(e))).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await s.u.post("/categories",e)},async update(e,a){await s.u.put("/categories/".concat(e),a)},async delete(e){await s.u.delete("/categories/".concat(e))}}},4975:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});var s=t(5731);let r={getAll:async()=>(await s.u.get("/campaigns")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/campaigns/paginated?".concat(a))},async getById(e){try{return(await s.u.get("/campaigns/".concat(e))).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await s.u.post("/campaigns",e)},async update(e,a){await s.u.put("/campaigns/".concat(e),a)},async delete(e){await s.u.delete("/campaigns/".concat(e))},getActive:async()=>(await s.u.get("/campaigns/active")).data||[]}},5868:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7550:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7559:(e,a,t)=>{"use strict";t.d(a,{A7:()=>x,Ay:()=>o,JH:()=>u,Qn:()=>m});var s=t(5155),r=t(5695),c=t(5809),n=t(7703),l=t(3741),i=t(7550),d=t(2657);function o(e){let{title:a,subtitle:t,loading:o=!1,error:u=null,onBack:m,backUrl:x,children:g,actions:p}=e,y=(0,r.useRouter)(),h=()=>{m?m():x?y.push(x):y.back()};return o?(0,s.jsx)(c.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):u?(0,s.jsx)(c.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(l.A,{variant:"secondary",onClick:h,className:"flex items-center",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Geri"]})}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-red-600 text-lg font-medium mb-2",children:"Hata"}),(0,s.jsx)("div",{className:"text-gray-600",children:u})]})})]})}):(0,s.jsx)(c.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(l.A,{variant:"secondary",onClick:h,className:"flex items-center",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:a})]}),t&&(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:t})]})]}),p&&(0,s.jsx)("div",{className:"flex items-center space-x-4",children:p})]}),g]})})}function u(e){let{title:a,children:t,className:r=""}=e;return(0,s.jsxs)(n.Zp,{className:r,children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:a})}),(0,s.jsx)(n.Wu,{children:t})]})}function m(e){let{label:a,value:t,className:r=""}=e;return(0,s.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-100 last:border-b-0 ".concat(r),children:[(0,s.jsxs)("span",{className:"text-gray-600 font-medium",children:[a,":"]}),(0,s.jsx)("span",{className:"text-gray-900",children:t})]})}function x(e){let{children:a,columns:t=2,className:r=""}=e;return(0,s.jsx)("div",{className:"grid ".concat({1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}[t]," gap-6 ").concat(r),children:a})}},7703:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>i,Zp:()=>n,aR:()=>l});var s=t(5155),r=t(2115),c=t(2596);let n=(0,r.forwardRef)((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsx)("div",{ref:a,className:(0,c.A)("bg-white rounded-lg border border-gray-200 shadow-sm",t),...n,children:r})});n.displayName="Card";let l=(0,r.forwardRef)((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsx)("div",{ref:a,className:(0,c.A)("px-6 py-4 border-b border-gray-200",t),...n,children:r})});l.displayName="CardHeader",(0,r.forwardRef)((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsx)("h3",{ref:a,className:(0,c.A)("text-lg font-semibold text-gray-900",t),...n,children:r})}).displayName="CardTitle",(0,r.forwardRef)((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsx)("p",{ref:a,className:(0,c.A)("text-sm text-gray-600 mt-1",t),...n,children:r})}).displayName="CardDescription";let i=(0,r.forwardRef)((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsx)("div",{ref:a,className:(0,c.A)("px-6 py-4",t),...n,children:r})});i.displayName="CardContent",(0,r.forwardRef)((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsx)("div",{ref:a,className:(0,c.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",t),...n,children:r})}).displayName="CardFooter"},9434:(e,a,t)=>{"use strict";function s(e){if(!e)return"-";try{let a=new Date(e);if(isNaN(a.getTime()))return e;return a.toLocaleString("tr-TR",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(a){return console.error("Error formatting date:",a),e}}function r(e){if(!e)return"";let a=e.replace(/\D/g,"");return 11===a.length&&a.startsWith("0")?"".concat(a.slice(0,4)," ").concat(a.slice(4,7)," ").concat(a.slice(7,9)," ").concat(a.slice(9,11)):10===a.length?"0".concat(a.slice(0,3)," ").concat(a.slice(3,6)," ").concat(a.slice(6,8)," ").concat(a.slice(8,10)):e}function c(e){if(!e)return"";let a=e.replace(/\D/g,"");return 11===a.length?"".concat(a.slice(0,3)," ").concat(a.slice(3,6)," ").concat(a.slice(6,9)," ").concat(a.slice(9,11)):e}t.d(a,{NZ:()=>c,Yq:()=>s,qH:()=>r})},9604:(e,a,t)=>{"use strict";t.d(a,{j:()=>r});var s=t(5731);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/products/paginated?".concat(a))},async getById(e){try{return(await s.u.get("/products/".concat(e))).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let a={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",a)},async update(e,a){let t={...a,campaign_id:a.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put("/products/".concat(e),t)},async delete(e){await s.u.delete("/products/".concat(e))},search:async e=>(await s.u.get("/products/search?q=".concat(encodeURIComponent(e)))).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}}},e=>{var a=a=>e(e.s=a);e.O(0,[122,809,441,684,977],()=>a(277)),_N_E=e.O()}]);