(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[358],{245:(e,s,r)=>{"use strict";r.d(s,{m:()=>a});var t=r(5731);let a={getAll:async()=>(await t.u.get("/customers")).data||[],async getPaginated(e){let s=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get("/customers/paginated?".concat(s))},async getById(e){try{return(await t.u.get("/customers/".concat(e))).data||null}catch(e){return console.error("Error fetching customer:",e),null}},async create(e){await t.u.post("/customers",e)},async update(e,s){await t.u.put("/customers/".concat(e),s)},async delete(e){await t.u.delete("/customers/".concat(e))},async searchByTC(e){if(!e||e.length<2)return[];try{return(await t.u.get("/customers/search/".concat(e))).data||[]}catch(e){return console.error("Error searching customers:",e),[]}}}},2492:(e,s,r)=>{Promise.resolve().then(r.bind(r,4608))},2525:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3717:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4608:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>y});var t=r(5155),a=r(2115),l=r(5695),c=r(7559),i=r(3741),n=r(245),d=r(9434),m=r(3717),x=r(2525),o=r(7580),h=r(1586),u=r(9420),j=r(4516),p=r(9074);function y(){let e=(0,l.useParams)().id,[s,r]=(0,a.useState)(null),[y,g]=(0,a.useState)(!0),[f,N]=(0,a.useState)(null),v=(0,a.useCallback)(async()=>{try{g(!0),N(null);let s=await n.m.getById(e);if(!s)return void N("M\xfcşteri bulunamadı.");r(s)}catch(e){console.error("Error loading customer:",e),N("M\xfcşteri bilgileri y\xfcklenirken bir hata oluştu.")}finally{g(!1)}},[e]);(0,a.useEffect)(()=>{v()},[v]);let b=s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,t.jsxs)(i.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,t.jsx)(c.Ay,{title:(null==s?void 0:s.name)||"M\xfcşteri Detayı",subtitle:"M\xfcşteri bilgilerini g\xf6r\xfcnt\xfcleyin",loading:y,error:f,backUrl:"/customers",actions:b,children:s&&(0,t.jsxs)(c.A7,{columns:2,children:[(0,t.jsx)(c.JH,{title:"Kişisel Bilgiler",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(c.Qn,{label:"Ad Soyad",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:s.name})]})}),(0,t.jsx)(c.Qn,{label:"TC Kimlik No",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-2 py-1 rounded",children:(0,d.NZ)(s.tc)})]})}),(0,t.jsx)(c.Qn,{label:"Telefon",value:s.phone?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,t.jsx)("a",{href:"tel:".concat(s.phone),className:"text-purple-600 hover:text-purple-800 font-medium",children:(0,d.qH)(s.phone)})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})})]})}),(0,t.jsx)(c.JH,{title:"İletişim Bilgileri",children:(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsx)(c.Qn,{label:"Adres",value:s.address?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm leading-relaxed",children:s.address})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Adres belirtilmemiş"})})})}),(0,t.jsx)(c.JH,{title:"Kayıt Bilgileri",className:"md:col-span-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(c.Qn,{label:"Kayıt Tarihi",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{children:(0,d.Yq)(s.created_at)})]})}),(0,t.jsx)(c.Qn,{label:"Son G\xfcncelleme",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{children:(0,d.Yq)(s.updated_at)})]})})]})}),(0,t.jsx)(c.JH,{title:"M\xfcşteri \xd6zeti",className:"md:col-span-2",children:(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:"M\xfcşteri Bilgi Kartı"})]}),(0,t.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Ad Soyad:"})," ",s.name]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"TC:"})," ",(0,d.NZ)(s.tc)]}),s.phone&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Telefon:"})," ",(0,d.qH)(s.phone)]}),s.address&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Adres:"})," ",s.address]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Kayıt:"})," ",(0,d.Yq)(s.created_at)]})]})]})})]})})}},7550:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7559:(e,s,r)=>{"use strict";r.d(s,{A7:()=>h,Ay:()=>m,JH:()=>x,Qn:()=>o});var t=r(5155),a=r(5695),l=r(5809),c=r(7703),i=r(3741),n=r(7550),d=r(2657);function m(e){let{title:s,subtitle:r,loading:m=!1,error:x=null,onBack:o,backUrl:h,children:u,actions:j}=e,p=(0,a.useRouter)(),y=()=>{o?o():h?p.push(h):p.back()};return m?(0,t.jsx)(l.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):x?(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(i.A,{variant:"secondary",onClick:y,className:"flex items-center",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Geri"]})}),(0,t.jsx)(c.Zp,{children:(0,t.jsxs)(c.Wu,{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-red-600 text-lg font-medium mb-2",children:"Hata"}),(0,t.jsx)("div",{className:"text-gray-600",children:x})]})})]})}):(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(i.A,{variant:"secondary",onClick:y,className:"flex items-center",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:s})]}),r&&(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:r})]})]}),j&&(0,t.jsx)("div",{className:"flex items-center space-x-4",children:j})]}),u]})})}function x(e){let{title:s,children:r,className:a=""}=e;return(0,t.jsxs)(c.Zp,{className:a,children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:s})}),(0,t.jsx)(c.Wu,{children:r})]})}function o(e){let{label:s,value:r,className:a=""}=e;return(0,t.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-100 last:border-b-0 ".concat(a),children:[(0,t.jsxs)("span",{className:"text-gray-600 font-medium",children:[s,":"]}),(0,t.jsx)("span",{className:"text-gray-900",children:r})]})}function h(e){let{children:s,columns:r=2,className:a=""}=e;return(0,t.jsx)("div",{className:"grid ".concat({1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}[r]," gap-6 ").concat(a),children:s})}},7703:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>n,Zp:()=>c,aR:()=>i});var t=r(5155),a=r(2115),l=r(2596);let c=(0,a.forwardRef)((e,s)=>{let{className:r,children:a,...c}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.A)("bg-white rounded-lg border border-gray-200 shadow-sm",r),...c,children:a})});c.displayName="Card";let i=(0,a.forwardRef)((e,s)=>{let{className:r,children:a,...c}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.A)("px-6 py-4 border-b border-gray-200",r),...c,children:a})});i.displayName="CardHeader",(0,a.forwardRef)((e,s)=>{let{className:r,children:a,...c}=e;return(0,t.jsx)("h3",{ref:s,className:(0,l.A)("text-lg font-semibold text-gray-900",r),...c,children:a})}).displayName="CardTitle",(0,a.forwardRef)((e,s)=>{let{className:r,children:a,...c}=e;return(0,t.jsx)("p",{ref:s,className:(0,l.A)("text-sm text-gray-600 mt-1",r),...c,children:a})}).displayName="CardDescription";let n=(0,a.forwardRef)((e,s)=>{let{className:r,children:a,...c}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.A)("px-6 py-4",r),...c,children:a})});n.displayName="CardContent",(0,a.forwardRef)((e,s)=>{let{className:r,children:a,...c}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",r),...c,children:a})}).displayName="CardFooter"},9074:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,s,r)=>{"use strict";function t(e){if(!e)return"-";try{let s=new Date(e);if(isNaN(s.getTime()))return e;return s.toLocaleString("tr-TR",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(s){return console.error("Error formatting date:",s),e}}function a(e){if(!e)return"";let s=e.replace(/\D/g,"");return 11===s.length&&s.startsWith("0")?"".concat(s.slice(0,4)," ").concat(s.slice(4,7)," ").concat(s.slice(7,9)," ").concat(s.slice(9,11)):10===s.length?"0".concat(s.slice(0,3)," ").concat(s.slice(3,6)," ").concat(s.slice(6,8)," ").concat(s.slice(8,10)):e}function l(e){if(!e)return"";let s=e.replace(/\D/g,"");return 11===s.length?"".concat(s.slice(0,3)," ").concat(s.slice(3,6)," ").concat(s.slice(6,9)," ").concat(s.slice(9,11)):e}r.d(s,{NZ:()=>l,Yq:()=>t,qH:()=>a})}},e=>{var s=s=>e(e.s=s);e.O(0,[122,809,441,684,977],()=>s(2492)),_N_E=e.O()}]);