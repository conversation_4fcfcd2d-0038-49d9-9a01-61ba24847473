(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{245:(e,a,t)=>{"use strict";t.d(a,{m:()=>s});var r=t(5731);let s={getAll:async()=>(await r.u.get("/customers")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/customers/paginated?".concat(a))},async getById(e){try{return(await r.u.get("/customers/".concat(e))).data||null}catch(e){return console.error("Error fetching customer:",e),null}},async create(e){await r.u.post("/customers",e)},async update(e,a){await r.u.put("/customers/".concat(e),a)},async delete(e){await r.u.delete("/customers/".concat(e))},async searchByTC(e){if(!e||e.length<2)return[];try{return(await r.u.get("/customers/search/".concat(e))).data||[]}catch(e){return console.error("Error searching customers:",e),[]}}}},2657:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9022:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>j});var r=t(5155),s=t(2115),l=t(5695),n=t(5809),c=t(3741),i=t(3915),d=t(6440),o=t(542),u=t(7580),h=t(2657),m=t(3717),g=t(2525),p=t(4616),x=t(7924),y=t(245);function j(){let e=(0,l.useRouter)(),[a,t]=(0,s.useState)([]),[j,v]=(0,s.useState)(!0),[f,A]=(0,s.useState)(!1),[N,k]=(0,s.useState)(!1),[C,w]=(0,s.useState)(null),[b,_]=(0,s.useState)({page:1,per_page:10}),[M,S]=(0,s.useState)(""),[E,z]=(0,s.useState)(""),[T,P]=(0,s.useState)({name:"",phone:"",tc:"",address:""});(0,s.useEffect)(()=>{K()},[]);let K=async()=>{try{v(!0);let e=await y.m.getAll();t(e)}catch(e){console.error("Error loading customers:",e)}finally{v(!1)}},O=()=>{S(E),_(e=>({...e,page:1}))},q=e=>{z(e)},D=async()=>{try{await y.m.create(T),A(!1),P({name:"",phone:"",tc:"",address:""}),K()}catch(e){console.error("Error creating customer:",e),alert("M\xfcşteri oluşturulurken hata oluştu: "+e.message)}},B=e=>{w(e),P({name:e.name,phone:e.phone,tc:e.tc,address:e.address}),k(!0)},Y=async()=>{if(C)try{let e={name:T.name,phone:T.phone,tc:T.tc};await y.m.update(C.id,e),k(!1),w(null),K()}catch(e){console.error("Error updating customer:",e),alert("M\xfcşteri g\xfcncellenirken hata oluştu: "+e.message)}},I=async e=>{if(confirm("Bu m\xfcşteriyi silmek istediğinizden emin misiniz?"))try{await y.m.delete(e),K()}catch(e){console.error("Error deleting customer:",e),alert("M\xfcşteri silinirken hata oluştu: "+e.message)}},L=e=>{let[a,t]=e.split(" "),[r,s,l]=a.split("-"),[n,c]=t.split(":");return"".concat(l,".").concat(s,".").concat(r," ").concat(n,":").concat(c)},R=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),G=a.filter(e=>{if(!M)return!0;let a=R(M);return R(e.name).includes(a)||R(e.phone).includes(a)||R(e.tc).includes(a)||e.address&&R(e.address).includes(a)}),H=[{key:"name",header:"M\xfcşteri",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name})]})},{key:"tc",header:"TC Kimlik",render:e=>e.tc},{key:"phone",header:"Telefon",render:e=>e.phone||"-"},{key:"address",header:"Adres",render:e=>e.address||"-"},{key:"created_at",header:"Kayıt Tarihi",render:e=>L(e.created_at)},{key:"actions",header:"İşlemler",className:"text-right",render:a=>(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(c.A,{size:"sm",variant:"success",onClick:()=>e.push("/customers/".concat(a.id)),title:"Detay",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(c.A,{size:"sm",variant:"info",onClick:()=>B(a),title:"D\xfczenle",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(c.A,{size:"sm",variant:"danger",onClick:()=>I(a.id),title:"Sil",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})}];return j?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,r.jsxs)(n.A,{children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"M\xfcşteriler"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Kayıtlı m\xfcşterileri g\xf6r\xfcnt\xfcleyin ve y\xf6netin"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(c.A,{onClick:()=>A(!0),children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Yeni M\xfcşteri"]})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"M\xfcşteri adı, telefon, TC veya adres ara...",value:E,onChange:e=>q(e.target.value),onKeyDown:e=>"Enter"===e.key&&O(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)(c.A,{onClick:O,variant:"primary",className:"px-4 py-2",children:"Ara"}),M&&(0,r.jsx)(c.A,{onClick:()=>{S(""),z(""),_(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),M&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[G.length,' m\xfcşteri g\xf6steriliyor • "',M,'" araması']})]}),(0,r.jsx)(o.A,{columns:H,data:G,pagination:{page:b.page,per_page:b.per_page,total:G.length,total_pages:Math.ceil(G.length/b.per_page),has_next:b.page<Math.ceil(G.length/b.per_page),has_prev:b.page>1},onPageChange:e=>{_(a=>({...a,page:e}))},onPerPageChange:e=>{_({page:1,per_page:e})},loading:j,emptyMessage:"Hen\xfcz m\xfcşteri kaydı bulunmuyor",emptyIcon:(0,r.jsx)(u.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0})]}),(0,r.jsx)(d.A,{isOpen:f,onClose:()=>{A(!1),P({name:"",phone:"",tc:"",address:""})},title:"Yeni M\xfcşteri Oluştur",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i.A,{label:"M\xfcşteri Adı",value:T.name,onChange:e=>P(a=>({...a,name:e.target.value})),required:!0}),(0,r.jsx)(i.A,{label:"Telefon",value:T.phone,onChange:e=>P(a=>({...a,phone:e.target.value}))}),(0,r.jsx)(i.A,{label:"TC Kimlik No",value:T.tc,onChange:e=>P(a=>({...a,tc:e.target.value})),required:!0}),(0,r.jsx)(i.A,{label:"Adres",value:T.address,onChange:e=>P(a=>({...a,address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(c.A,{variant:"secondary",onClick:()=>{A(!1),P({name:"",phone:"",tc:"",address:""})},children:"İptal"}),(0,r.jsx)(c.A,{onClick:D,children:"Oluştur"})]})]})}),(0,r.jsx)(d.A,{isOpen:N,onClose:()=>{k(!1),w(null)},title:"M\xfcşteri D\xfczenle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i.A,{label:"M\xfcşteri Adı",value:T.name,onChange:e=>P(a=>({...a,name:e.target.value})),required:!0}),(0,r.jsx)(i.A,{label:"Telefon",value:T.phone,onChange:e=>P(a=>({...a,phone:e.target.value}))}),(0,r.jsx)(i.A,{label:"TC Kimlik No",value:T.tc,onChange:e=>P(a=>({...a,tc:e.target.value})),required:!0}),(0,r.jsx)(i.A,{label:"Adres",value:T.address,onChange:e=>P(a=>({...a,address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(c.A,{variant:"secondary",onClick:()=>{k(!1),w(null)},children:"İptal"}),(0,r.jsx)(c.A,{onClick:Y,children:"G\xfcncelle"})]})]})})]})}},9858:(e,a,t)=>{Promise.resolve().then(t.bind(t,9022))}},e=>{var a=a=>e(e.s=a);e.O(0,[122,269,809,564,441,684,977],()=>a(9858)),_N_E=e.O()}]);