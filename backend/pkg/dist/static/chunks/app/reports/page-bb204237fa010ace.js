(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{3109:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3368:(e,t,a)=>{Promise.resolve().then(a.bind(a,6349))},4957:(e,t,a)=>{"use strict";a.d(t,{U:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/categories")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/categories/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/categories/".concat(e))).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await s.u.post("/categories",e)},async update(e,t){await s.u.put("/categories/".concat(e),t)},async delete(e){await s.u.delete("/categories/".concat(e))}}},4975:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/campaigns")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/campaigns/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/campaigns/".concat(e))).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await s.u.post("/campaigns",e)},async update(e,t){await s.u.put("/campaigns/".concat(e),t)},async delete(e){await s.u.delete("/campaigns/".concat(e))},getActive:async()=>(await s.u.get("/campaigns/active")).data||[]}},6349:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var s=a(5155),r=a(2115),l=a(5809),n=a(7703),c=a(3741),i=a(7108),d=a(1586),o=a(8048),m=a(7580),x=a(2713),h=a(9946);let u=(0,h.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);var g=a(7109),p=a(9074);function y(e){let{onDateChange:t,showMonthFilter:a=!0}=e,l=new Date().getFullYear(),n=new Date().getMonth()+1,[c,i]=(0,r.useState)(l),[d,o]=(0,r.useState)(n),m=Array.from({length:5},(e,t)=>l-t),x=e=>{i(e),t(e,a?d:0)},h=e=>{o(e),t(c,e)};return(0,s.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Tarih Filtresi:"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("select",{value:c,onChange:e=>x(parseInt(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:m.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),a&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-gray-500",children:"-"}),(0,s.jsx)("select",{value:d,onChange:e=>h(parseInt(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[{value:1,label:"Ocak"},{value:2,label:"Şubat"},{value:3,label:"Mart"},{value:4,label:"Nisan"},{value:5,label:"Mayıs"},{value:6,label:"Haziran"},{value:7,label:"Temmuz"},{value:8,label:"Ağustos"},{value:9,label:"Eyl\xfcl"},{value:10,label:"Ekim"},{value:11,label:"Kasım"},{value:12,label:"Aralık"}].map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]})}let j=(0,h.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var f=a(3109),N=a(7809),v=a(5731),w=a(9509);let b={async getTopSellingProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.year&&t.append("year",e.year.toString()),e.month&&t.append("month",e.month.toString()),e.limit&&t.append("limit",e.limit.toString());let a="/sales/reports/top-selling".concat(t.toString()?"?"+t.toString():"");return(await v.u.get(a)).data||[]},async getMonthlySales(e){let t=new URLSearchParams;e&&t.append("year",e.toString());let a="/sales/reports/monthly".concat(t.toString()?"?"+t.toString():"");return(await v.u.get(a)).data||[]},async getCategorySales(e,t){let a=new URLSearchParams;e&&a.append("year",e.toString()),t&&a.append("month",t.toString());let s="/sales/reports/category".concat(a.toString()?"?"+a.toString():"");return(await v.u.get(s)).data||[]},async exportMonthlySales(e,t){let a=new URLSearchParams;e&&a.append("year",e.toString()),t&&a.append("month",t.toString());let s="/sales/reports/export/monthly".concat(a.toString()?"?"+a.toString():"");try{let e=await fetch("".concat(w.env.NEXT_PUBLIC_API_URL||"http://localhost:8080/api").concat(s),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=e.headers.get("content-disposition"),a="aylik_satislar.xlsx";if(t){let e=t.match(/filename=(.+)/);e&&(a=e[1])}let r=await e.blob(),l=window.URL.createObjectURL(r),n=document.createElement("a");n.href=l,n.download=a,document.body.appendChild(n),n.click(),n.remove(),window.URL.revokeObjectURL(l)}catch(e){throw console.error("Export error:",e),e}}};function S(e){let{year:t,month:a,limit:l=5}=e,[c,d]=(0,r.useState)([]),[o,m]=(0,r.useState)(!0),x=(0,r.useCallback)(async()=>{try{m(!0);let e=await b.getTopSellingProducts({year:t||void 0,month:a||void 0,limit:l});d(e)}catch(e){console.error("Error loading top selling products:",e)}finally{m(!1)}},[t,a,l]);return((0,r.useEffect)(()=>{x()},[x]),o)?(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(j,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"En \xc7ok Satılan \xdcr\xfcnler"})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Y\xfckleniyor..."})})})]}):(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(j,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"En \xc7ok Satılan \xdcr\xfcnler"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:a?"".concat(a,"/").concat(t):t})]})}),(0,s.jsx)(n.Wu,{children:c.length>0?(0,s.jsx)("div",{className:"space-y-4",children:c.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3 ".concat(0===t?"bg-yellow-100 text-yellow-600":1===t?"bg-gray-100 text-gray-600":2===t?"bg-orange-100 text-orange-600":"bg-blue-100 text-blue-600"),children:t<3?(0,s.jsx)(j,{className:"h-4 w-4"}):(0,s.jsx)("span",{className:"text-sm font-medium",children:t+1})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.product_name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Kod: ",e.product_code]})]})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900 flex items-center",children:[(0,s.jsx)(i.A,{className:"h-3 w-3 mr-1"}),e.total_quantity]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Adet"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-green-600 flex items-center",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),e.total_revenue.toFixed(2)," TL"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Gelir"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-blue-600 flex items-center",children:[(0,s.jsx)(N.A,{className:"h-3 w-3 mr-1"}),e.sales_count]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Satış"})]})]})})]},e.product_id))}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(i.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Bu d\xf6nemde satış verisi bulunamadı"})]})})]})}function A(e){let{year:t,month:a}=e,[l,c]=(0,r.useState)([]),[d,o]=(0,r.useState)(!0),m=(0,r.useCallback)(async()=>{try{o(!0);let e=await b.getCategorySales(t||void 0,a||void 0);c(e)}catch(e){console.error("Error loading category sales:",e)}finally{o(!1)}},[t,a]);if((0,r.useEffect)(()=>{m()},[m]),d)return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Kategori Bazlı Satışlar"})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Y\xfckleniyor..."})})})]});let h=Math.max(...l.map(e=>e.total_revenue),1);return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Kategori Bazlı Satışlar"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:a?"".concat(a,"/").concat(t):t})]})}),(0,s.jsx)(n.Wu,{children:l.length>0?(0,s.jsx)("div",{className:"max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,s.jsx)("div",{className:"space-y-4 pr-2",children:l.map((e,t)=>{let a=e.total_revenue/h*100;return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)("span",{className:"text-xs font-medium text-blue-600",children:t+1})}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.category_name})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(i.A,{className:"h-3 w-3 mr-1"}),e.total_sales," adet"]}),(0,s.jsxs)("div",{className:"flex items-center text-green-600 font-medium",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),e.total_revenue.toFixed(2)," TL"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(a,"%")}})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,s.jsxs)("span",{children:[e.product_count," \xfcr\xfcn"]}),(0,s.jsxs)("span",{children:["%",(e.total_revenue/l.reduce((e,t)=>e+t.total_revenue,0)*100).toFixed(1)]})]})]},e.category_id)})})}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Bu d\xf6nemde kategori satış verisi bulunamadı"})]})})]})}function _(){let[e,t]=(0,r.useState)(!0),[a,h]=(0,r.useState)({totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}),[p,j]=(0,r.useState)([]),[f,N]=(0,r.useState)([]),v=new Date().getFullYear(),w=new Date().getMonth()+1,[_,k]=(0,r.useState)(v),[R,E]=(0,r.useState)(w);(0,r.useEffect)(()=>{(async()=>{try{t(!0);let[e,,a,s]=await Promise.all([g.y.getStats(),g.y.getTopSellingProducts(5),g.y.getRecentActivities(5),b.getMonthlySales(_)]);h(e),j(a),N(s)}catch(e){console.error("Error fetching reports data:",e)}finally{t(!1)}})()},[_]);let P=async()=>{try{t(!0),await b.exportMonthlySales(_,R)}catch(e){console.error("Export error:",e),alert("Excel dosyası indirilemedi: "+e.message)}finally{t(!1)}};return e?(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Raporlar"}),(0,s.jsx)("p",{className:"text-gray-600",children:"İşletmenizin performans raporlarını g\xf6r\xfcnt\xfcleyin"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(i.A,{className:"h-8 w-8 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam \xdcr\xfcn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.totalProducts}),(0,s.jsxs)("p",{className:"text-xs text-blue-600 flex items-center",children:[(0,s.jsx)(i.A,{className:"h-3 w-3 mr-1"}),a.totalCategories," kategori"]})]})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Bor\xe7"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",a.unpaidDebtsAmount.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-red-600 flex items-center",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"\xd6denmemiş bor\xe7lar"]})]})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Kasa Bakiyesi"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",a.totalSafeAmount.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center",children:[(0,s.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Toplam nakit"]})]})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Aktif Kampanya"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.activeCampaigns}),(0,s.jsxs)("p",{className:"text-xs text-purple-600 flex items-center",children:[(0,s.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Devam eden"]})]})]})})})]}),(0,s.jsx)(y,{onDateChange:(e,t)=>{k(e),E(t)}}),(0,s.jsx)(S,{year:_,month:R,limit:5}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Aylık Satış Trendi"})]}),(0,s.jsxs)(c.A,{onClick:P,disabled:e||0===f.length,className:"flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 text-sm",children:[(0,s.jsx)(u,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:[R,"/",_," Excel İndir"]})]})]})}),(0,s.jsx)(n.Wu,{children:f.length>0?(0,s.jsx)("div",{className:"space-y-4",children:f.slice(0,6).map(e=>{let t=Math.max(...f.map(e=>e.total_revenue),1),a=e.total_revenue/t*100;return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.month,"/",e.year]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,s.jsxs)("span",{className:"text-gray-600",children:[e.total_sales," adet"]}),(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:[e.total_revenue.toFixed(2)," TL"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(a,"%")}})})]},"".concat(e.year,"-").concat(e.month))})}):(0,s.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Satış verisi bulunamadı"})]})})})]}),(0,s.jsx)(A,{year:_,month:R})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-6",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son İşlemler"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:p.length>0?p.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.description}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]}),(0,s.jsxs)("div",{className:"text-right",children:[e.amount&&(0,s.jsx)("p",{className:"text-sm font-medium ".concat(e.amount.startsWith("+")?"text-green-600":e.amount.startsWith("-")?"text-red-600":"text-gray-900"),children:e.amount}),(0,s.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.type})]})]},e.id)):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz işlem bulunmuyor"})})})})]})}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Mali \xd6zet"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Toplam Kasa"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["₺",a.totalSafeAmount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-green-600",children:"Mevcut nakit"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Toplam Bor\xe7"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:["₺",a.unpaidDebtsAmount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-red-600",children:"\xd6denmemiş bor\xe7lar"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Net Durum"}),(0,s.jsxs)("p",{className:"text-2xl font-bold ".concat(a.totalSafeAmount-a.unpaidDebtsAmount>=0?"text-green-600":"text-red-600"),children:["₺",(a.totalSafeAmount-a.unpaidDebtsAmount).toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-blue-600",children:"Kasa - Bor\xe7"})]})]})})]})]})})}},7109:(e,t,a)=>{"use strict";a.d(t,{y:()=>i});var s=a(9604),r=a(7634),l=a(9486),n=a(4957),c=a(4975);let i={async getStats(){try{let[e,t,a,i,d]=await Promise.all([s.j.getAll(),r.J.getAll(),l.x.getAll(),n.U.getAll(),c.A.getAll()]),o=a.reduce((e,t)=>e+t.amount,0),m=t.filter(e=>!e.is_paid).reduce((e,t)=>e+t.amount,0),x=new Date,h=d.filter(e=>new Date(e.end_date)>x).length;return{totalProducts:e.length,totalDebts:m,totalSafeAmount:o,unpaidDebtsAmount:m,totalCategories:i.length,activeCampaigns:h}}catch(e){return console.error("Error fetching dashboard stats:",e),{totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}}},async getRecentProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{return(await s.j.getAll()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent products:",e),[]}},async getRecentDebts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{return(await r.J.getUnpaid()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent debts:",e),[]}},async getTopSellingProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{return(await s.j.getAll()).sort((e,t)=>t.quantity-e.quantity).slice(0,e)}catch(e){return console.error("Error fetching top selling products:",e),[]}},async getRecentActivities(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{let[t,a]=await Promise.all([this.getRecentProducts(3),this.getRecentDebts(2)]),s=[];return t.forEach(e=>{s.push({id:e.id,type:"product",description:"Yeni \xfcr\xfcn eklendi: ".concat(e.name),amount:"₺".concat(e.price),time:this.formatTimeAgo(e.created_at)})}),a.forEach(e=>{s.push({id:e.id,type:"debt",description:"Yeni bor\xe7 kaydı: ".concat(e.name," ").concat(e.surname),amount:"₺".concat(e.amount),time:this.formatTimeAgo(e.created_at)})}),s.sort(()=>Math.random()-.5).slice(0,e)}catch(e){return console.error("Error fetching recent activities:",e),[]}},formatTimeAgo(e){let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/36e5);if(a<1)return"Az \xf6nce";{if(a<24)return"".concat(a," saat \xf6nce");let e=Math.floor(a/24);return"".concat(e," g\xfcn \xf6nce")}}}},7634:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/debts")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/debts/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/debts/".concat(e))).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await s.u.post("/debts",e)},async update(e,t){await s.u.put("/debts/".concat(e),t)},async delete(e){await s.u.delete("/debts/".concat(e))},async pay(e,t){await s.u.post("/debts/".concat(e,"/pay"),t)},getUnpaid:async()=>(await s.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await s.u.get("/debts/paid")).data||[],async getByDateRange(e,t){let a=new URLSearchParams;return e&&a.append("start_date",e),t&&a.append("end_date",t),(await s.u.get("/debts/filter/date-range?".concat(a.toString()))).data||[]},getByPaymentStatus:async e=>(await s.u.get("/debts/filter/payment-status?is_paid=".concat(e))).data||[]}},7703:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>i,Zp:()=>n,aR:()=>c});var s=a(5155),r=a(2115),l=a(2596);let n=(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.A)("bg-white rounded-lg border border-gray-200 shadow-sm",a),...n,children:r})});n.displayName="Card";let c=(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.A)("px-6 py-4 border-b border-gray-200",a),...n,children:r})});c.displayName="CardHeader",(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,l.A)("text-lg font-semibold text-gray-900",a),...n,children:r})}).displayName="CardTitle",(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,l.A)("text-sm text-gray-600 mt-1",a),...n,children:r})}).displayName="CardDescription";let i=(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.A)("px-6 py-4",a),...n,children:r})});i.displayName="CardContent",(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",a),...n,children:r})}).displayName="CardFooter"},9074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9486:(e,t,a)=>{"use strict";a.d(t,{x:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/safes")).data||[],async getById(e){let t=await s.u.get("/safes/".concat(e));if(!t.data)throw Error("Safe not found");return t.data},async create(e){await s.u.post("/safes",e)},async update(e,t){await s.u.put("/safes/".concat(e),t)},async delete(e){await s.u.delete("/safes/".concat(e))},async addMoney(e,t){await s.u.post("/safes/".concat(e,"/add-money"),t)},async withdrawMoney(e,t){await s.u.post("/safes/".concat(e,"/withdraw-money"),t)},getTotalAmount:async()=>(await s.u.get("/safes/total")).total_amount||0}},9604:(e,t,a)=>{"use strict";a.d(t,{j:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/products/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/products/".concat(e))).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put("/products/".concat(e),a)},async delete(e){await s.u.delete("/products/".concat(e))},search:async e=>(await s.u.get("/products/search?q=".concat(encodeURIComponent(e)))).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}}},e=>{var t=t=>e(e.s=t);e.O(0,[122,809,441,684,977],()=>t(3368)),_N_E=e.O()}]);