(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[750],{1154:(e,a,s)=>{Promise.resolve().then(s.bind(s,9931))},3109:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3717:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3915:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});var t=s(5155),l=s(2115),r=s(2596);let n=(0,l.forwardRef)((e,a)=>{let{className:s,label:l,error:n,helperText:i,type:c="text",...d}=e;return(0,t.jsxs)("div",{className:"w-full",children:[l&&(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:l}),(0,t.jsx)("input",{type:c,className:(0,r.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",n&&"border-red-300 focus:ring-red-500 focus:border-red-500",s),ref:a,...d}),n&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n}),i&&!n&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:i})]})});n.displayName="Input";let i=n},6440:(e,a,s)=>{"use strict";s.d(a,{A:()=>d});var t=s(5155),l=s(2115),r=s(5939),n=s(280),i=s(4416),c=s(3741);function d(e){let{isOpen:a,onClose:s,title:d,children:o,size:x="md"}=e;return(0,t.jsx)(r.e,{appear:!0,show:a,as:l.Fragment,children:(0,t.jsxs)(n.lG,{as:"div",className:"relative z-50",onClose:s,children:[(0,t.jsx)(r.e.Child,{as:l.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,t.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,t.jsx)(r.e.Child,{as:l.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,t.jsxs)(n.lG.Panel,{className:"w-full ".concat({sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[x]," transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,t.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),(0,t.jsx)(c.A,{variant:"secondary",size:"sm",onClick:s,className:"p-2",children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"px-6 py-4",children:o})]})})})})]})})}},7703:(e,a,s)=>{"use strict";s.d(a,{Wu:()=>c,Zp:()=>n,aR:()=>i});var t=s(5155),l=s(2115),r=s(2596);let n=(0,l.forwardRef)((e,a)=>{let{className:s,children:l,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.A)("bg-white rounded-lg border border-gray-200 shadow-sm",s),...n,children:l})});n.displayName="Card";let i=(0,l.forwardRef)((e,a)=>{let{className:s,children:l,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.A)("px-6 py-4 border-b border-gray-200",s),...n,children:l})});i.displayName="CardHeader",(0,l.forwardRef)((e,a)=>{let{className:s,children:l,...n}=e;return(0,t.jsx)("h3",{ref:a,className:(0,r.A)("text-lg font-semibold text-gray-900",s),...n,children:l})}).displayName="CardTitle",(0,l.forwardRef)((e,a)=>{let{className:s,children:l,...n}=e;return(0,t.jsx)("p",{ref:a,className:(0,r.A)("text-sm text-gray-600 mt-1",s),...n,children:l})}).displayName="CardDescription";let c=(0,l.forwardRef)((e,a)=>{let{className:s,children:l,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.A)("px-6 py-4",s),...n,children:l})});c.displayName="CardContent",(0,l.forwardRef)((e,a)=>{let{className:s,children:l,...n}=e;return(0,t.jsx)("div",{ref:a,className:(0,r.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",s),...n,children:l})}).displayName="CardFooter"},9486:(e,a,s)=>{"use strict";s.d(a,{x:()=>l});var t=s(5731);let l={getAll:async()=>(await t.u.get("/safes")).data||[],async getById(e){let a=await t.u.get("/safes/".concat(e));if(!a.data)throw Error("Safe not found");return a.data},async create(e){await t.u.post("/safes",e)},async update(e,a){await t.u.put("/safes/".concat(e),a)},async delete(e){await t.u.delete("/safes/".concat(e))},async addMoney(e,a){await t.u.post("/safes/".concat(e,"/add-money"),a)},async withdrawMoney(e,a){await t.u.post("/safes/".concat(e,"/withdraw-money"),a)},getTotalAmount:async()=>(await t.u.get("/safes/total")).total_amount||0}},9931:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>j});var t=s(5155),l=s(2115),r=s(5809),n=s(7703),i=s(3741),c=s(6440),d=s(3915),o=s(4616),x=s(8048),m=s(3109);let h=(0,s(9946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var u=s(3717),y=s(2525),p=s(9486);function j(){let[e,a]=(0,l.useState)([]),[s,j]=(0,l.useState)(0),[g,f]=(0,l.useState)(!0),[v,N]=(0,l.useState)(!1),[w,b]=(0,l.useState)(!1),[k,A]=(0,l.useState)(!1),[C,T]=(0,l.useState)(!1),[S,R]=(0,l.useState)(null),[z,E]=(0,l.useState)(null),[F,K]=(0,l.useState)({amount:0}),[M,O]=(0,l.useState)(0);(0,l.useEffect)(()=>{L()},[]);let L=async()=>{try{f(!0);let[e,s]=await Promise.all([p.x.getAll(),p.x.getTotalAmount()]);a(e),j(s)}catch(e){console.error("Error loading data:",e)}finally{f(!1)}},_=async()=>{try{await p.x.create(F),N(!1),K({amount:0}),L()}catch(e){console.error("Error creating safe:",e)}},B=async()=>{if(S)try{let e={amount:F.amount};await p.x.update(S.id,e),b(!1),R(null),L()}catch(e){console.error("Error updating safe:",e)}},P=async e=>{if(confirm("Bu kasayı silmek istediğinizden emin misiniz?"))try{await p.x.delete(e),L()}catch(e){console.error("Error deleting safe:",e)}},D=async()=>{if(z)try{await p.x.addMoney(z.id,{amount:M}),A(!1),E(null),O(0),L()}catch(e){console.error("Error adding money:",e)}},G=async()=>{if(z)try{await p.x.withdrawMoney(z.id,{amount:M}),T(!1),E(null),O(0),L()}catch(e){console.error("Error withdrawing money:",e)}},Y=e=>{R(e),K({amount:e.amount}),b(!0)},W=e=>{E(e),O(0),A(!0)},Z=e=>{E(e),O(0),T(!0)};return g?(0,t.jsx)(r.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,t.jsx)(r.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kasa Y\xf6netimi"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Kasalarınızı ve para akışınızı y\xf6netin"})]}),(0,t.jsxs)(i.A,{onClick:()=>N(!0),children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Yeni Kasa"]})]}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Toplam Bakiye"})}),(0,t.jsxs)("p",{className:"text-4xl font-bold text-green-600",children:["₺",s.toLocaleString("tr-TR")]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[e.length," kasa"]})]})})})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsxs)("h3",{className:"font-medium text-gray-900",children:["Kasa #",e.id.slice(-8)]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.A,{size:"sm",variant:"success",onClick:()=>W(e),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"danger",onClick:()=>Z(e),children:(0,t.jsx)(h,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"secondary",onClick:()=>Y(e),children:(0,t.jsx)(u.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"danger",onClick:()=>P(e.id),children:(0,t.jsx)(y.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-3xl font-bold text-green-600",children:["₺",e.amount.toLocaleString("tr-TR")]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Mevcut Bakiye"})]}),(0,t.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Oluşturulma:"}),(0,t.jsx)("span",{children:new Date(e.created_at).toLocaleDateString("tr-TR")})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Son G\xfcncelleme:"}),(0,t.jsx)("span",{children:new Date(e.updated_at).toLocaleDateString("tr-TR")})]})]})]})})]},e.id))}),0===e.length&&(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"text-center py-12",children:[(0,t.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz kasa yok"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"İlk kasanızı oluşturarak başlayın"}),(0,t.jsxs)(i.A,{onClick:()=>N(!0),children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Yeni Kasa Oluştur"]})]})}),(0,t.jsx)(c.A,{isOpen:v,onClose:()=>N(!1),title:"Yeni Kasa Oluştur",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(d.A,{label:"Başlangı\xe7 Tutarı",type:"number",value:F.amount,onChange:e=>K({...F,amount:parseFloat(e.target.value)||0}),placeholder:"0.00",helperText:"Kasanın başlangı\xe7 bakiyesini girin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>N(!1),children:"İptal"}),(0,t.jsx)(i.A,{onClick:_,children:"Kasa Oluştur"})]})]})}),(0,t.jsx)(c.A,{isOpen:w,onClose:()=>b(!1),title:"Kasa D\xfczenle",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(d.A,{label:"Bakiye",type:"number",value:F.amount,onChange:e=>K({...F,amount:parseFloat(e.target.value)||0}),placeholder:"0.00",helperText:"Kasanın mevcut bakiyesini g\xfcncelleyin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>b(!1),children:"İptal"}),(0,t.jsx)(i.A,{onClick:B,children:"G\xfcncelle"})]})]})}),(0,t.jsx)(c.A,{isOpen:k,onClose:()=>A(!1),title:"Para Ekle",children:(0,t.jsx)("div",{className:"space-y-4",children:z&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900",children:["Kasa #",z.id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Mevcut Bakiye: ₺",z.amount.toLocaleString("tr-TR")]})]}),(0,t.jsx)(d.A,{label:"Eklenecek Tutar",type:"number",value:M,onChange:e=>O(parseFloat(e.target.value)||0),placeholder:"0.00",helperText:"Kasaya eklemek istediğiniz tutarı girin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>A(!1),children:"İptal"}),(0,t.jsx)(i.A,{variant:"success",onClick:D,children:"Para Ekle"})]})]})})}),(0,t.jsx)(c.A,{isOpen:C,onClose:()=>T(!1),title:"Para \xc7ek",children:(0,t.jsx)("div",{className:"space-y-4",children:z&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900",children:["Kasa #",z.id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Mevcut Bakiye: ₺",z.amount.toLocaleString("tr-TR")]})]}),(0,t.jsx)(d.A,{label:"\xc7ekilecek Tutar",type:"number",value:M,onChange:e=>O(parseFloat(e.target.value)||0),placeholder:"0.00",helperText:"Maksimum: ₺".concat(z.amount.toLocaleString("tr-TR"))}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>T(!1),children:"İptal"}),(0,t.jsx)(i.A,{variant:"danger",onClick:G,children:"Para \xc7ek"})]})]})})})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[122,269,809,441,684,977],()=>a(1154)),_N_E=e.O()}]);