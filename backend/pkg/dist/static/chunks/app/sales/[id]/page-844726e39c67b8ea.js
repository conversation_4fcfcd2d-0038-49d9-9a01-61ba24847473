(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[489],{903:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>b});var t=s(5155),l=s(2115),r=s(5695),n=s(7559),i=s(3741),c=s(2814),d=s(3892),m=s(9604),o=s(9434),u=s(3717),x=s(2525),g=s(7108),p=s(5868),h=s(7580),j=s(1586),y=s(9420),_=s(4516),N=s(9074),v=s(6308);function b(){let e=(0,r.useParams)().id,[a,s]=(0,l.useState)(null),[b,f]=(0,l.useState)(null),[w,S]=(0,l.useState)(!0),[A,T]=(0,l.useState)(null),k=(0,l.useCallback)(async()=>{try{S(!0),T(null);let a=await d.T.getById(e);if(!a)return void T("Satış bulunamadı.");if(s(a),a.product_id){let e=await m.j.getById(a.product_id);e&&f(e)}}catch(e){console.error("Error loading sale:",e),T("Satış bilgileri y\xfcklenirken bir hata oluştu.")}finally{S(!1)}},[e]);(0,l.useEffect)(()=>{k()},[k]);let B=()=>a?a.campaign_discount_amount+a.seller_discount_amount:0,R=a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(i.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,t.jsxs)(i.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,t.jsx)(n.Ay,{title:"Satış #".concat((null==a?void 0:a.id.slice(-8))||"Detayı"),subtitle:"Satış bilgilerini g\xf6r\xfcnt\xfcleyin",loading:w,error:A,backUrl:"/sales",actions:R,children:a&&(0,t.jsxs)(n.A7,{columns:2,children:[(0,t.jsx)(n.JH,{title:"\xdcr\xfcn Bilgileri",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"\xdcr\xfcn Adı",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:a.product_name})]})}),b&&(0,t.jsx)(n.Qn,{label:"\xdcr\xfcn Kodu",value:(0,t.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-2 py-1 rounded",children:b.product_code})}),(0,t.jsx)(n.Qn,{label:"Miktar",value:(0,t.jsxs)("span",{className:"font-semibold text-blue-600",children:[a.quantity," adet"]})}),(0,t.jsx)(n.Qn,{label:"Birim Fiyat",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,t.jsxs)("span",{children:["₺",a.unit_price.toLocaleString("tr-TR")]})]})})]})}),(0,t.jsx)(n.JH,{title:"M\xfcşteri Bilgileri",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"Ad Soyad",value:a.customer_name?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:a.customer_name})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})}),(0,t.jsx)(n.Qn,{label:"TC Kimlik",value:a.customer_tc?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{className:"font-mono text-sm",children:(0,o.NZ)(a.customer_tc)})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})}),(0,t.jsx)(n.Qn,{label:"Telefon",value:a.customer_phone?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,t.jsx)("span",{children:(0,o.qH)(a.customer_phone)})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})}),(0,t.jsx)(n.Qn,{label:"Adres",value:a.customer_address?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:a.customer_address})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})})]})}),(0,t.jsxs)(n.JH,{title:"Fiyat Hesaplamaları",className:"md:col-span-2",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Orijinal Fiyat"}),(0,t.jsxs)("div",{className:"text-lg font-semibold text-blue-600",children:["₺",a.original_price.toLocaleString("tr-TR")]})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Kampanya İndirimi"}),(0,t.jsxs)("div",{className:"text-lg font-semibold text-orange-600",children:["₺",a.campaign_discount_amount.toLocaleString("tr-TR")]})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Satıcı İndirimi"}),(0,t.jsxs)("div",{className:"text-lg font-semibold text-purple-600",children:["₺",a.seller_discount_amount.toLocaleString("tr-TR")]})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Toplam Tutar"}),(0,t.jsxs)("div",{className:"text-lg font-semibold text-green-600",children:["₺",a.total_price.toLocaleString("tr-TR")]})]})]}),B()>0&&(0,t.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,t.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,t.jsx)("strong",{children:"Toplam İndirim:"})," ₺",B().toLocaleString("tr-TR"),"(",a.campaign_discount_amount>0&&"Kampanya: ₺".concat(a.campaign_discount_amount.toLocaleString("tr-TR")),a.campaign_discount_amount>0&&a.seller_discount_amount>0&&", ",a.seller_discount_amount>0&&"Satıcı: ₺".concat(a.seller_discount_amount.toLocaleString("tr-TR")),")"]})})]}),(0,t.jsx)(n.JH,{title:"Satış Bilgileri",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"Satış Tarihi",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{children:(0,o.Yq)(a.sale_date)})]})}),(0,t.jsx)(n.Qn,{label:"\xd6deme Durumu",value:a?a.is_paid?(0,t.jsx)(c.E,{variant:"success",children:"\xd6dendi"}):(0,t.jsx)(c.E,{variant:"danger",children:"\xd6denmedi"}):null}),(0,t.jsx)(n.Qn,{label:"Oluşturulma",value:(0,o.Yq)(a.created_at)}),(0,t.jsx)(n.Qn,{label:"Son G\xfcncelleme",value:(0,o.Yq)(a.updated_at)})]})}),(0,t.jsx)(n.JH,{title:"Satış \xd6zeti",children:(0,t.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Satış Fişi"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"\xdcr\xfcn:"})," ",a.product_name]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Miktar:"})," ",a.quantity," adet"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Birim Fiyat:"})," ₺",a.unit_price.toLocaleString("tr-TR")]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Toplam:"})," ₺",a.total_price.toLocaleString("tr-TR")]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Durum:"})," ",a.is_paid?"\xd6dendi":"\xd6denmedi"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tarih:"})," ",(0,o.Yq)(a.sale_date)]})]})]})})]})})}},3892:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(5731);let l={getAll:async()=>(await t.u.get("/sales")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get("/sales/paginated?".concat(a))},async getById(e){try{return(await t.u.get("/sales/".concat(e))).data||null}catch(e){return console.error("Error fetching sale:",e),null}},async create(e){await t.u.post("/sales",e)},async createMulti(e){await t.u.post("/sales/multi",e)},async update(e,a){await t.u.put("/sales/".concat(e),a)},async delete(e){await t.u.delete("/sales/".concat(e))}}},4367:(e,a,s)=>{Promise.resolve().then(s.bind(s,903))},4516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},9604:(e,a,s)=>{"use strict";s.d(a,{j:()=>l});var t=s(5731);let l={getAll:async()=>(await t.u.get("/products")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get("/products/paginated?".concat(a))},async getById(e){try{return(await t.u.get("/products/".concat(e))).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let a={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await t.u.post("/products",a)},async update(e,a){let s={...a,campaign_id:a.campaign_id||"00000000-0000-0000-0000-000000000000"};await t.u.put("/products/".concat(e),s)},async delete(e){await t.u.delete("/products/".concat(e))},search:async e=>(await t.u.get("/products/search?q=".concat(encodeURIComponent(e)))).data||[],importFromExcel:async e=>(await t.u.post("/products/import-excel",e)).data}}},e=>{var a=a=>e(e.s=a);e.O(0,[122,809,41,441,684,977],()=>a(4367)),_N_E=e.O()}]);