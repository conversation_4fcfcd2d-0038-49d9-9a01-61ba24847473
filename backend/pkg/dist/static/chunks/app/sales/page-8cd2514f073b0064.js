(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[117],{245:(e,t,a)=>{"use strict";a.d(t,{m:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/customers")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/customers/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/customers/".concat(e))).data||null}catch(e){return console.error("Error fetching customer:",e),null}},async create(e){await s.u.post("/customers",e)},async update(e,t){await s.u.put("/customers/".concat(e),t)},async delete(e){await s.u.delete("/customers/".concat(e))},async searchByTC(e){if(!e||e.length<2)return[];try{return(await s.u.get("/customers/search/".concat(e))).data||[]}catch(e){return console.error("Error searching customers:",e),[]}}}},2657:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3892:(e,t,a)=>{"use strict";a.d(t,{T:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/sales")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/sales/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/sales/".concat(e))).data||null}catch(e){return console.error("Error fetching sale:",e),null}},async create(e){await s.u.post("/sales",e)},async createMulti(e){await s.u.post("/sales/multi",e)},async update(e,t){await s.u.put("/sales/".concat(e),t)},async delete(e){await s.u.delete("/sales/".concat(e))}}},5342:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>k});var s=a(5155),r=a(2115),i=a(5695),l=a(5809),n=a(3741),c=a(6440),d=a(3915),o=a(542),u=a(7809),m=a(2657),p=a(3717),x=a(2525),h=a(4616),g=a(7924),y=a(3892),_=a(9604);function j(e){let{onProductSelect:t,placeholder:a="\xdcr\xfcn adı veya kodu arayın...",disabled:i=!1}=e,[l,n]=(0,r.useState)(""),[c,d]=(0,r.useState)([]),[o,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(!1),x=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=e=>{x.current&&!x.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.useEffect)(()=>{let e=setTimeout(async()=>{if(""===l.trim()||l.trim().length<2){d([]),u(!1);return}try{p(!0);let e=await _.j.search(l);d(e),u(e.length>0)}catch(e){console.error("Error searching products:",e),d([]),u(!1)}finally{p(!1)}},300);return()=>clearTimeout(e)},[l]);let h=e=>{n(e)},g=e=>{t(e),n(""),d([]),u(!1)};return(0,s.jsxs)("div",{ref:x,className:"relative",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:l,onChange:e=>h(e.target.value),placeholder:a,disabled:i,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"}),m&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"})})]}),o&&c.length>0&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto",children:c.map(e=>(0,s.jsx)("div",{onClick:()=>g(e),className:"px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Kod: ",e.product_code]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.discounted_price<e.price?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"line-through text-gray-400",children:[e.price.toFixed(2)," TL"]}),(0,s.jsxs)("span",{className:"ml-1 text-green-600",children:[e.discounted_price.toFixed(2)," TL"]})]}):(0,s.jsxs)("span",{children:[e.price.toFixed(2)," TL"]})}),(0,s.jsxs)("div",{className:"text-xs ".concat(e.quantity>0?"text-green-600":"text-red-500"),children:["Stok: ",e.quantity," ",0===e.quantity&&"(T\xfckendi)"]})]})]})},e.id))}),o&&0===c.length&&""!==l.trim()&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg",children:(0,s.jsx)("div",{className:"px-3 py-2 text-gray-500 text-center",children:"\xdcr\xfcn bulunamadı"})})]})}function v(e){let{items:t,products:a,onUpdateItem:r,onRemoveItem:i,totalAmount:l}=e,c=(e,s)=>{let i=t[e],l=a.find(e=>e.id===i.product_id);if(!l)return void console.error("Product not found for item:",i.product_id);if(s>l.quantity)return void alert("Bu \xfcr\xfcn i\xe7in maksimum ".concat(l.quantity," adet satış yapabilirsiniz. Mevcut stok: ").concat(l.quantity));s<1&&(s=1);let n=i.unit_price*s;r(e,{quantity:s,total_price:n})},o=(e,a)=>{let s=a*t[e].quantity;r(e,{unit_price:a,total_price:s})},u=(e,a)=>{let s=t[e];r(e,{unit_price:s.quantity>0?a/s.quantity:0,total_price:a})};return 0===t.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Hen\xfcz \xfcr\xfcn eklenmedi. Yukarıdaki arama kutusundan \xfcr\xfcn arayarak ekleyebilirsiniz."}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Se\xe7ilen \xdcr\xfcnler"}),(0,s.jsx)("div",{className:"space-y-3",children:t.map((e,t)=>{let l=a.find(t=>t.id===e.product_id),m=(null==l?void 0:l.quantity)||0;return(0,s.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:e.product_name}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["Stok: ",m]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-3 mt-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Miktar"}),(0,s.jsx)(d.A,{type:"number",min:"1",max:m,value:e.quantity,onChange:e=>c(t,parseInt(e.target.value)||1),className:"text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Birim Fiyat"}),(0,s.jsx)(d.A,{type:"number",step:"0.01",min:"0",value:e.unit_price,onChange:e=>o(t,parseFloat(e.target.value)||0),className:"text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Satıcı İndirimi"}),(0,s.jsx)(d.A,{type:"number",step:"0.01",min:"0",value:e.seller_discount_amount||0,onChange:e=>r(t,{seller_discount_amount:parseFloat(e.target.value)||0}),className:"text-sm",placeholder:"0.00"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Toplam"}),(0,s.jsx)(d.A,{type:"number",step:"0.01",min:"0",value:e.total_price.toFixed(2),onChange:e=>u(t,parseFloat(e.target.value)||0),className:"text-sm"})]})]}),(e.campaign_discount_amount||e.original_price)&&(0,s.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,s.jsx)("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"Kampanya Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-3 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Orijinal Fiyat:"}),(0,s.jsxs)("span",{className:"ml-1 font-medium",children:[(e.original_price||0).toFixed(2)," TL"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Kampanya İndirimi:"}),(0,s.jsxs)("span",{className:"ml-1 font-medium",children:[(e.campaign_discount_amount||0).toFixed(2)," TL"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Kasaya Gidecek:"}),(0,s.jsxs)("span",{className:"ml-1 font-medium text-green-600",children:[(e.total_price+(e.campaign_discount_amount||0)).toFixed(2)," TL"]})]})]})]})]}),(0,s.jsx)(n.A,{variant:"secondary",size:"sm",onClick:()=>i(t),className:"ml-3 text-red-600 hover:text-red-700 hover:bg-red-50",children:"Kaldır"})]})},t)})}),(0,s.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-lg font-medium text-gray-900",children:"Genel Toplam:"}),(0,s.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:[l.toFixed(2)," TL"]})]})})]})}var b=a(245),f=a(7580);function N(e){let{value:t,onChange:a,onCustomerSelect:i,placeholder:l="TC Kimlik No",label:n="TC Kimlik No",required:c=!1}=e,[d,o]=(0,r.useState)([]),[u,m]=(0,r.useState)(!1),[p,x]=(0,r.useState)(!1),h=(0,r.useRef)(null),g=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=setTimeout(async()=>{if(t.length>=2){x(!0);try{let e=await b.m.searchByTC(t);o(e),m(e.length>0)}catch(e){console.error("Error searching customers:",e),o([]),m(!1)}finally{x(!1)}}else o([]),m(!1)},300);return()=>clearTimeout(e)},[t]),(0,r.useEffect)(()=>{let e=e=>{g.current&&!g.current.contains(e.target)&&h.current&&!h.current.contains(e.target)&&m(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let y=e=>{a(e.tc),i(e),m(!1)};return(0,s.jsxs)("div",{className:"relative w-full",children:[n&&(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n,c&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{ref:h,type:"text",value:t,onChange:e=>{a(e.target.value)},placeholder:l,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",onFocus:()=>{d.length>0&&m(!0)}}),p&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"})})]}),u&&d.length>0&&(0,s.jsx)("div",{ref:g,className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto",children:d.map(e=>(0,s.jsx)("div",{onClick:()=>y(e),className:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-gray-400 mr-3"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["TC: ",e.tc,e.phone&&" • Tel: ".concat(e.phone),e.address&&" • Adres: ".concat(e.address)]})]})]})},e.id))})]})}var w=a(283);function k(){let e=(0,i.useRouter)(),{user:t}=(0,w.A)(),[a,b]=(0,r.useState)([]),[f,k]=(0,r.useState)([]),[A,S]=(0,r.useState)(!0),[C,T]=(0,r.useState)(!1),[q,E]=(0,r.useState)(!1),[z,M]=(0,r.useState)(null),[F,I]=(0,r.useState)({page:1,per_page:10}),[L,B]=(0,r.useState)(""),[P,D]=(0,r.useState)(""),[O,R]=(0,r.useState)({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0}),[K,U]=(0,r.useState)({product_id:"",quantity:1,unit_price:0,total_price:0,customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0});(0,r.useEffect)(()=>{Y()},[]);let Y=async()=>{try{S(!0);let[e,t]=await Promise.all([y.T.getAll(),_.j.getAll()]);b(e),k(t)}catch(e){console.error("Error loading data:",e)}finally{S(!1)}},G=()=>{B(P),I(e=>({...e,page:1}))},H=e=>{D(e)},J=e=>{let t=f.find(t=>t.id===e);if(t){let a=t.discounted_price<t.price?t.discounted_price:t.price;U(t=>({...t,product_id:e,unit_price:a,total_price:a*t.quantity}))}},Q=e=>{U(t=>({...t,quantity:e,total_price:t.unit_price*e}))},V=(e,t)=>{R(a=>{let s=[...a.items];s[e]={...s[e],...t};let r=s.reduce((e,t)=>e+t.total_price,0);return{...a,items:s,total_amount:r}})},W=async()=>{if(0===O.items.length)return void alert("En az bir \xfcr\xfcn eklemelisiniz.");try{let e=O.sale_date+" "+new Date().toTimeString().split(" ")[0],a={...O,sale_date:e,organization_id:(null==t?void 0:t.organization_id)||""};await y.T.createMulti(a),T(!1),R({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0}),Y()}catch(e){console.error("Error creating multi sale:",e),alert("Satış oluşturulurken hata oluştu: "+e.message)}},X=e=>{M(e),U({product_id:e.product_id,quantity:e.quantity,unit_price:e.unit_price,total_price:e.total_price,customer_name:e.customer_name,customer_phone:e.customer_phone,customer_tc:e.customer_tc,customer_address:e.customer_address,sale_date:e.sale_date.split(" ")[0],is_paid:e.is_paid}),E(!0)},Z=async()=>{if(z)try{let e=K.sale_date;e.includes(" ")||(e=K.sale_date+" "+new Date().toTimeString().split(" ")[0]);let t={quantity:K.quantity,unit_price:K.unit_price,total_price:K.total_price,customer_name:K.customer_name,customer_phone:K.customer_phone,customer_tc:K.customer_tc,sale_date:e};await y.T.update(z.id,t),E(!1),M(null),Y()}catch(e){console.error("Error updating sale:",e)}},$=async e=>{if(confirm("Bu satışı silmek istediğinizden emin misiniz?"))try{await y.T.delete(e),Y()}catch(e){console.error("Error deleting sale:",e)}},ee=e=>{if(e.includes(" ")){let[t,a]=e.split(" "),[s,r,i]=t.split("-"),[l,n]=a.split(":");return"".concat(i,".").concat(r,".").concat(s," ").concat(l,":").concat(n)}let[t,a,s]=e.split("-");return"".concat(s,".").concat(a,".").concat(t)},et=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),ea=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),es=a.filter(e=>{if(!L)return!0;let t=ea(L);return ea(e.customer_name).includes(t)||ea(e.customer_phone).includes(t)||ea(e.customer_tc).includes(t)||ea(e.product_name).includes(t)}),er=[{key:"customer",header:"M\xfcşteri",render:e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.customer_name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.customer_phone})]})]})},{key:"product",header:"\xdcr\xfcn",render:e=>(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.product_name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Adet: ",e.quantity]})]})},{key:"amount",header:"Tutar",render:e=>(0,s.jsx)("div",{className:"font-medium",children:et(e.total_price)})},{key:"payment",header:"\xd6deme",render:e=>(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat(e.is_paid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_paid?"\xd6dendi":"\xd6denmedi"})},{key:"date",header:"Tarih",render:e=>(0,s.jsx)("div",{className:"text-sm",children:ee(e.sale_date)})},{key:"actions",header:"İşlemler",className:"text-right",render:t=>(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(n.A,{size:"sm",variant:"success",onClick:()=>e.push("/sales/".concat(t.id)),title:"Detay",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.A,{size:"sm",variant:"info",onClick:()=>X(t),title:"D\xfczenle",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.A,{size:"sm",variant:"danger",onClick:()=>$(t.id),title:"Sil",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})}];return A?(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,s.jsxs)(l.A,{children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Satışlar"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Satış işlemlerini y\xf6netin"})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(n.A,{onClick:()=>T(!0),children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Yeni Satış"]})})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"M\xfcşteri adı, telefon, TC veya \xfcr\xfcn ara...",value:P,onChange:e=>H(e.target.value),onKeyDown:e=>"Enter"===e.key&&G(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)(n.A,{onClick:G,variant:"primary",className:"px-4 py-2",children:"Ara"}),L&&(0,s.jsx)(n.A,{onClick:()=>{B(""),D(""),I(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),L&&(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[es.length,' satış g\xf6steriliyor • "',L,'" araması']})]}),(0,s.jsx)(o.A,{columns:er,data:es,pagination:{page:F.page,per_page:F.per_page,total:es.length,total_pages:Math.ceil(es.length/F.per_page),has_next:F.page<Math.ceil(es.length/F.per_page),has_prev:F.page>1},onPageChange:e=>{I(t=>({...t,page:e}))},onPerPageChange:e=>{I({page:1,per_page:e})},loading:A,emptyMessage:"Hen\xfcz satış kaydı bulunmuyor",emptyIcon:(0,s.jsx)(u.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0})]}),(0,s.jsx)(c.A,{isOpen:C,onClose:()=>{T(!1),R({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0})},title:"Yeni Satış",size:"lg",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Arama"}),(0,s.jsx)(j,{onProductSelect:e=>{if(e.quantity<=0)return void alert('"'.concat(e.name,'" \xfcr\xfcn\xfc stokta bulunmuyor. Mevcut stok: ').concat(e.quantity));let t=O.items.findIndex(t=>t.product_id===e.id);if(-1!==t){let a=O.items[t],s=a.quantity+1;return s>e.quantity?void alert("Bu \xfcr\xfcn i\xe7in maksimum ".concat(e.quantity," adet satış yapabilirsiniz. Şu anda ").concat(a.quantity," adet ekli.")):void V(t,{quantity:s,total_price:a.unit_price*s})}let a=e.discounted_price<e.price?e.discounted_price:e.price,s={product_id:e.id,product_name:e.name,quantity:1,unit_price:a,total_price:a};R(e=>({...e,items:[...e.items,s],total_amount:e.total_amount+a}))}})]}),(0,s.jsx)(v,{items:O.items,products:f,onUpdateItem:V,onRemoveItem:e=>{R(t=>{let a=t.items.filter((t,a)=>a!==e),s=a.reduce((e,t)=>e+t.total_price,0);return{...t,items:a,total_amount:s}})},totalAmount:O.total_amount}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"M\xfcşteri Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(d.A,{label:"M\xfcşteri Adı",value:O.customer_name,onChange:e=>R(t=>({...t,customer_name:e.target.value}))}),(0,s.jsx)(d.A,{label:"M\xfcşteri Telefonu",value:O.customer_phone,onChange:e=>R(t=>({...t,customer_phone:e.target.value}))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,s.jsx)(N,{label:"M\xfcşteri TC",value:O.customer_tc||"",onChange:e=>R(t=>({...t,customer_tc:e})),onCustomerSelect:e=>{R(t=>({...t,customer_name:e.name,customer_phone:e.phone,customer_tc:e.tc,customer_address:e.address}))}}),(0,s.jsx)(d.A,{label:"Satış Tarihi",type:"date",value:O.sale_date,onChange:e=>R(t=>({...t,sale_date:e.target.value}))})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(d.A,{label:"M\xfcşteri Adresi",value:O.customer_address,onChange:e=>R(t=>({...t,customer_address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."})}),(0,s.jsxs)("div",{className:"space-y-2 mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"\xd6deme Durumu"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status",checked:O.is_paid,onChange:()=>R(e=>({...e,is_paid:!0})),className:"mr-2"}),"\xd6dendi"]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status",checked:!O.is_paid,onChange:()=>R(e=>({...e,is_paid:!1})),className:"mr-2"}),"\xd6denmedi (Bor\xe7)"]})]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,s.jsx)(n.A,{variant:"secondary",onClick:()=>{T(!1),R({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0})},className:"flex-1",children:"İptal"}),(0,s.jsx)(n.A,{onClick:W,className:"flex-1",disabled:0===O.items.length,children:"Satış Oluştur"})]})]})}),(0,s.jsx)(c.A,{isOpen:q,onClose:()=>E(!1),title:"Satışı D\xfczenle",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"\xdcr\xfcn"}),(0,s.jsxs)("select",{value:K.product_id,onChange:e=>J(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"",children:"\xdcr\xfcn se\xe7in"}),f.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," (Stok: ",e.quantity,")"]},e.id))]})]}),(0,s.jsx)(d.A,{label:"Miktar",type:"number",min:"1",value:K.quantity,onChange:e=>Q(parseInt(e.target.value)||1)}),(0,s.jsx)(d.A,{label:"Birim Fiyat",type:"number",step:"0.01",value:K.unit_price,onChange:e=>U(t=>({...t,unit_price:parseFloat(e.target.value)||0,total_price:(parseFloat(e.target.value)||0)*t.quantity}))}),(0,s.jsx)(d.A,{label:"Toplam Fiyat",type:"number",step:"0.01",value:K.total_price,readOnly:!0}),(0,s.jsx)(d.A,{label:"M\xfcşteri Adı",value:K.customer_name,onChange:e=>U(t=>({...t,customer_name:e.target.value}))}),(0,s.jsx)(d.A,{label:"M\xfcşteri Telefonu",value:K.customer_phone,onChange:e=>U(t=>({...t,customer_phone:e.target.value}))}),(0,s.jsx)(N,{label:"M\xfcşteri TC",value:K.customer_tc||"",onChange:e=>U(t=>({...t,customer_tc:e})),onCustomerSelect:e=>{U(t=>({...t,customer_name:e.name,customer_phone:e.phone,customer_tc:e.tc,customer_address:e.address}))}}),(0,s.jsx)(d.A,{label:"M\xfcşteri Adresi",value:K.customer_address,onChange:e=>U(t=>({...t,customer_address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."}),(0,s.jsx)(d.A,{label:"Satış Tarihi",type:"date",value:K.sale_date,onChange:e=>U(t=>({...t,sale_date:e.target.value}))}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"\xd6deme Durumu"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status_edit",checked:K.is_paid,onChange:()=>U(e=>({...e,is_paid:!0})),className:"mr-2"}),"\xd6dendi"]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status_edit",checked:!K.is_paid,onChange:()=>U(e=>({...e,is_paid:!1})),className:"mr-2"}),"\xd6denmedi (Bor\xe7)"]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,s.jsx)(n.A,{variant:"secondary",onClick:()=>E(!1),className:"flex-1",children:"İptal"}),(0,s.jsx)(n.A,{onClick:Z,className:"flex-1",disabled:!K.product_id||K.quantity<1,children:"G\xfcncelle"})]})]})})]})}},9604:(e,t,a)=>{"use strict";a.d(t,{j:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/products/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/products/".concat(e))).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put("/products/".concat(e),a)},async delete(e){await s.u.delete("/products/".concat(e))},search:async e=>(await s.u.get("/products/search?q=".concat(encodeURIComponent(e)))).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}},9857:(e,t,a)=>{Promise.resolve().then(a.bind(a,5342))}},e=>{var t=t=>e(e.s=t);e.O(0,[122,269,809,564,441,684,977],()=>t(9857)),_N_E=e.O()}]);