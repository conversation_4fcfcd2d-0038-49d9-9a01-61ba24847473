(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{1007:(e,a,r)=>{"use strict";r.d(a,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1457:(e,a,r)=>{Promise.resolve().then(r.bind(r,2899))},2657:(e,a,r)=>{"use strict";r.d(a,{A:()=>n});let n=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2899:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>f});var n=r(5155),s=r(2115),l=r(5695),i=r(2752),t=r(5876),o=r(3741),c=r(3915),d=r(6440),u=r(542),g=r(5525),h=r(1007),m=r(2525),p=r(3717),x=r(2657),y=r(4616),v=r(7924),j=r(9959),b=r(6488);function f(){let e=(0,l.useRouter)(),[a,r]=(0,s.useState)([]),[f,w]=(0,s.useState)([]),[z,_]=(0,s.useState)(!0),[N,k]=(0,s.useState)(!1),[A,C]=(0,s.useState)(!1),[O,K]=(0,s.useState)(null),[S,E]=(0,s.useState)({page:1,per_page:10}),[P,q]=(0,s.useState)(""),[Y,R]=(0,s.useState)(""),[U,B]=(0,s.useState)({username:"",password:"",role:"user",organization_id:""}),[M,D]=(0,s.useState)({username:"",password:"",role:"user",is_active:!0,organization_id:""});(0,s.useEffect)(()=>{L(),T()},[]);let L=async()=>{try{_(!0);let e=await j.y.getAllUsers();r(e)}catch(e){console.error("Error loading users:",e)}finally{_(!1)}},T=async()=>{try{let e=await b.h.getAllOrganizations();w(e)}catch(e){console.error("Error loading organizations:",e)}},F=()=>{q(Y),E(e=>({...e,page:1}))},G=e=>{R(e)},H=async()=>{try{await j.y.createUser(U),k(!1),B({username:"",password:"",role:"user",organization_id:""}),L()}catch(e){console.error("Error creating user:",e),alert("Kullanıcı oluşturulurken hata oluştu: "+e.message)}},I=e=>{K(e),D({username:e.username,password:"",role:e.role,is_active:e.is_active,organization_id:e.organization_id}),C(!0)},J=async()=>{if(O)try{let e={...M};e.password||delete e.password,await j.y.updateUser(O.id,e),C(!1),K(null),D({username:"",password:"",role:"user",is_active:!0,organization_id:""}),L()}catch(e){console.error("Error updating user:",e),alert("Kullanıcı g\xfcncellenirken hata oluştu: "+e.message)}},Q=async e=>{if(confirm("Bu kullanıcıyı silmek istediğinizden emin misiniz?"))try{await j.y.deleteUser(e),L()}catch(e){console.error("Error deleting user:",e),alert("Kullanıcı silinirken hata oluştu: "+e.message)}},V=a=>{e.push("/admin/users/".concat(a))},W=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),X=a.filter(e=>{if(!P)return!0;let a=W(P);return W(e.username).includes(a)||W(e.role).includes(a)}),Z=(S.page-1)*S.per_page,$=Z+S.per_page,ee=X.slice(Z,$),ea=Math.ceil(X.length/S.per_page),er=[{key:"username",header:"Kullanıcı Adı",render:e=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0 h-8 w-8",children:(0,n.jsx)("div",{className:"h-8 w-8 rounded-full flex items-center justify-center ".concat("admin"===e.role?"bg-purple-100":"bg-blue-100"),children:"admin"===e.role?(0,n.jsx)(g.A,{className:"h-4 w-4 ".concat("admin"===e.role?"text-purple-600":"text-blue-600")}):(0,n.jsx)(h.A,{className:"h-4 w-4 text-blue-600"})})}),(0,n.jsx)("div",{className:"ml-3",children:(0,n.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.username})})]})},{key:"role",header:"Rol",render:e=>(0,n.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("admin"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"),children:"admin"===e.role?"Admin":"Kullanıcı"})},{key:"organization",header:"Organizasyon",render:e=>{let a=f.find(a=>a.id===e.organization_id);return(0,n.jsx)("div",{className:"text-sm text-gray-900",children:a?a.name:"Bilinmiyor"})}},{key:"is_active",header:"Durum",render:e=>(0,n.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Aktif":"Pasif"})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>e.created_at},{key:"actions",header:"İşlemler",render:e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2 justify-end",children:[(0,n.jsx)(o.A,{variant:"danger",size:"sm",onClick:()=>Q(e.id),children:(0,n.jsx)(m.A,{className:"h-4 w-4"})}),(0,n.jsx)(o.A,{variant:"info",size:"sm",onClick:()=>I(e),children:(0,n.jsx)(p.A,{className:"h-4 w-4"})}),(0,n.jsx)(o.A,{variant:"success",size:"sm",onClick:()=>V(e.id),children:(0,n.jsx)(x.A,{className:"h-4 w-4"})})]})}];return z?(0,n.jsx)(t.A,{requireAdmin:!0,children:(0,n.jsx)(i.A,{children:(0,n.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,n.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,n.jsx)(t.A,{requireAdmin:!0,children:(0,n.jsx)(i.A,{children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kullanıcı Y\xf6netimi"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Sistem kullanıcılarını y\xf6netin"})]}),(0,n.jsxs)(o.A,{onClick:()=>k(!0),children:[(0,n.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Yeni Kullanıcı"]})]}),(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,n.jsx)("input",{type:"text",placeholder:"Kullanıcı adı veya rol ara...",value:Y,onChange:e=>G(e.target.value),onKeyDown:e=>"Enter"===e.key&&F(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,n.jsx)(o.A,{onClick:F,variant:"primary",className:"px-4 py-2",children:"Ara"}),P&&(0,n.jsx)(o.A,{onClick:()=>{q(""),R(""),E(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),P&&(0,n.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[X.length,' kullanıcı g\xf6steriliyor • "',P,'" araması']})]}),(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,n.jsx)(u.A,{data:ee,columns:er,pagination:{page:S.page,per_page:S.per_page,total:X.length,total_pages:ea,has_next:S.page<ea,has_prev:S.page>1},onPageChange:e=>{E(a=>({...a,page:e}))},onPerPageChange:e=>{E({page:1,per_page:e})}})}),(0,n.jsx)(d.A,{isOpen:N,onClose:()=>k(!1),title:"Yeni Kullanıcı Oluştur",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(c.A,{label:"Kullanıcı Adı",value:U.username,onChange:e=>B({...U,username:e.target.value}),placeholder:"Kullanıcı adını girin",required:!0}),(0,n.jsx)(c.A,{label:"Şifre",type:"password",value:U.password,onChange:e=>B({...U,password:e.target.value}),placeholder:"Şifreyi girin",required:!0}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol"}),(0,n.jsxs)("select",{value:U.role,onChange:e=>B({...U,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"user",children:"Kullanıcı"}),(0,n.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Organizasyon *"}),(0,n.jsxs)("select",{value:U.organization_id,onChange:e=>B({...U,organization_id:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,n.jsx)("option",{value:"",children:"Organizasyon se\xe7in"}),f.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,n.jsx)(o.A,{onClick:()=>k(!1),variant:"secondary",children:"İptal"}),(0,n.jsx)(o.A,{onClick:H,variant:"primary",children:"Oluştur"})]})]})}),(0,n.jsx)(d.A,{isOpen:A,onClose:()=>C(!1),title:"Kullanıcı D\xfczenle",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(c.A,{label:"Kullanıcı Adı",value:M.username,onChange:e=>D({...M,username:e.target.value}),placeholder:"Kullanıcı adını girin",required:!0}),(0,n.jsx)(c.A,{label:"Yeni Şifre (Boş bırakılırsa değişmez)",type:"password",value:M.password,onChange:e=>D({...M,password:e.target.value}),placeholder:"Yeni şifreyi girin"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol"}),(0,n.jsxs)("select",{value:M.role,onChange:e=>D({...M,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"user",children:"Kullanıcı"}),(0,n.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Organizasyon"}),(0,n.jsxs)("select",{value:M.organization_id,onChange:e=>D({...M,organization_id:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,n.jsx)("option",{value:"",children:"Organizasyon se\xe7in"}),f.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",id:"is_active",checked:M.is_active,onChange:e=>D({...M,is_active:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-gray-900",children:"Aktif kullanıcı"})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,n.jsx)(o.A,{onClick:()=>C(!1),variant:"secondary",children:"İptal"}),(0,n.jsx)(o.A,{onClick:J,variant:"primary",children:"G\xfcncelle"})]})]})})]})})})}},6488:(e,a,r)=>{"use strict";r.d(a,{h:()=>s});var n=r(5731);let s={async createOrganization(e){await n.u.post("/organizations",e)},async getAllOrganizations(){try{let e=await n.u.get("/organizations");return(null==e?void 0:e.data)||[]}catch(e){return console.error("Error fetching organizations:",e),[]}},async getOrganizationsPaginated(e){try{let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()}),r=await n.u.get("/organizations/paginated?".concat(a));return(null==r?void 0:r.data)||{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}catch(e){return console.error("Error fetching paginated organizations:",e),{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}},async getOrganizationById(e){try{let a=await n.u.get("/organizations/".concat(e));return null==a?void 0:a.data}catch(e){throw console.error("Error fetching organization by id:",e),e}},async updateOrganization(e,a){await n.u.put("/organizations/".concat(e),a)},async deleteOrganization(e){await n.u.delete("/organizations/".concat(e))},async createSubOrganization(e,a){await n.u.post("/organizations/".concat(e,"/sub-organizations"),a)},async getSubOrganizations(e){try{let a=await n.u.get("/organizations/".concat(e,"/sub-organizations"));return(null==a?void 0:a.data)||[]}catch(e){return console.error("Error fetching sub organizations:",e),[]}},async getMainOrganizations(){try{let e=await n.u.get("/organizations/main");return(null==e?void 0:e.data)||[]}catch(e){return console.error("Error fetching main organizations:",e),[]}}}}},e=>{var a=a=>e(e.s=a);e.O(0,[122,269,564,329,441,684,977],()=>a(1457)),_N_E=e.O()}]);