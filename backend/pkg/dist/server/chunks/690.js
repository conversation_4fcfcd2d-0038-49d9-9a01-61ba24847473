exports.id=690,exports.ids=[690],exports.modules={308:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},535:(e,t,s)=>{"use strict";s.d(t,{y:()=>a});var r=s(2185);let a={async login(e){let t=await r.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await r.u.post("/users",e)},getAllUsers:async()=>(await r.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get(`/users/paginated?${t}`)},async getUserById(e){let t=await r.u.get(`/users/${e}`);if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await r.u.put(`/users/${e}`,t)},async deleteUser(e){await r.u.delete(`/users/${e}`)}}},1135:()=>{},1372:(e,t,s)=>{Promise.resolve().then(s.bind(s,9131))},2185:(e,t,s)=>{"use strict";s.d(t,{u:()=>a});class r{constructor(e){this.baseURL=e}async request(e,t={}){let s=`${this.baseURL}${e}`,r=localStorage.getItem("auth_token"),a={headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`},...t.headers},...t};try{let e=await fetch(s,a);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let s=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(([t,s])=>{null!=s&&e.append(t,String(s))});let r=e.toString();r&&(s+=`?${r}`)}return this.request(s,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}}let a=new r("http://localhost:5555/api/v1")},2643:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(687),a=s(3210),n=s(9384);let i=(0,a.forwardRef)(({className:e,variant:t="primary",size:s="md",loading:a,children:i,disabled:l,...o},c)=>(0,r.jsxs)("button",{className:(0,n.A)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500":"primary"===t,"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500":"secondary"===t,"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500":"danger"===t,"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500":"success"===t,"bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500":"warning"===t,"bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500":"info"===t,"px-3 py-1.5 text-sm":"sm"===s,"px-4 py-2 text-sm":"md"===s,"px-6 py-3 text-base":"lg"===s},e),disabled:l||a,ref:c,...o,children:[a&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]}));i.displayName="Button";let l=i},3213:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,AuthProvider:()=>l});var r=s(687),a=s(3210),n=s(535);let i=(0,a.createContext)(void 0);function l({children:e}){let[t,s]=(0,a.useState)(null),[l,o]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[h,m]=(0,a.useState)(!0),u=async e=>{try{let t=await n.y.login(e);d(t.token),s(t.user),o(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},x={user:t,organization:l,token:c,isLoading:h,isAuthenticated:!!t&&!!c,isAdmin:t?.role==="admin",login:u,logout:()=>{d(null),s(null),o(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,r.jsx)(i.Provider,{value:x,children:e})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var r=s(7413),a=s(2202),n=s.n(a),i=s(4988),l=s.n(i),o=s(9131);s(1135);let c={title:"Business Management System",description:"Comprehensive business management solution for inventory, debt, and financial tracking"};function d({children:e}){return(0,r.jsx)("html",{lang:"tr",children:(0,r.jsx)("body",{className:`${n().variable} ${l().variable} antialiased bg-gray-50 min-h-screen`,children:(0,r.jsx)(o.AuthProvider,{children:e})})})}},4570:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(687);s(3210);var a=s(6189),n=s(3213);function i({children:e,requireAdmin:t=!1}){let{isAuthenticated:s,isAdmin:i,isLoading:l}=(0,n.A)();return((0,a.useRouter)(),l)?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):s&&(!t||i)?(0,r.jsx)(r.Fragment,{children:e}):null}},8100:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},8220:(e,t,s)=>{Promise.resolve().then(s.bind(s,3213))},8383:(e,t,s)=>{"use strict";s.d(t,{A:()=>y});var r=s(687),a=s(3210),n=s(5814),i=s.n(n),l=s(6189),o=s(3213),c=s(2192);let d=(0,s(2688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);var h=s(1312),m=s(9891),u=s(1860),x=s(83),g=s(2941),f=s(4952),b=s(2643);function y({children:e}){let[t,s]=(0,a.useState)(!1),{user:n,logout:y}=(0,o.A)(),p=(0,l.useRouter)(),v=(0,l.usePathname)(),j=()=>{y(),p.push("/login")},N=[{name:"Dashboard",href:"/admin",icon:c.A},{name:"Organizasyon Y\xf6netimi",href:"/admin/organizations",icon:d},{name:"Kullanıcı Y\xf6netimi",href:"/admin/users",icon:h.A}],w=e=>"/admin"===e?"/admin"===v:v.startsWith(e);return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsxs)("div",{className:`fixed inset-0 z-50 lg:hidden ${t?"block":"hidden"}`,children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>s(!1)}),(0,r.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Admin Panel"})]}),(0,r.jsx)("button",{onClick:()=>s(!1),children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-gray-400"})})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-4",children:(0,r.jsx)("ul",{className:"space-y-2",children:N.map(e=>(0,r.jsx)("li",{children:(0,r.jsxs)(i(),{href:e.href,className:`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${w(e.href)?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100"}`,onClick:()=>s(!1),children:[(0,r.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]})},e.name))})})]})]}),(0,r.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,r.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center px-4 border-b border-gray-200",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsx)("span",{className:"ml-2 text-lg font-semibold text-gray-900",children:"Admin Panel"})]}),(0,r.jsx)("nav",{className:"flex-1 px-4 py-4",children:(0,r.jsx)("ul",{className:"space-y-2",children:N.map(e=>(0,r.jsx)("li",{children:(0,r.jsxs)(i(),{href:e.href,className:`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${w(e.href)?"bg-blue-100 text-blue-700":"text-gray-700 hover:bg-gray-100"}`,children:[(0,r.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]})},e.name))})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 p-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:n?.username}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Admin"})]})]}),(0,r.jsxs)(b.A,{onClick:j,variant:"secondary",size:"sm",className:"mt-3 w-full",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"\xc7ıkış Yap"]})]})]})}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[(0,r.jsx)("div",{className:"sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("button",{onClick:()=>s(!0),className:"lg:hidden",children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,r.jsx)(i(),{href:"/admin",className:"hover:text-gray-700",children:"Admin"}),"/admin"!==v&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-gray-900",children:N.find(e=>v.startsWith(e.href))?.name||"Sayfa"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Hoş geldin, ",n?.username]}),(0,r.jsx)(b.A,{onClick:j,variant:"secondary",size:"sm",className:"lg:hidden",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]})]})}),(0,r.jsx)("main",{className:"p-4 sm:p-6 lg:p-8",children:e})]})]})}},9131:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>a});var r=s(2907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/contexts/AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/contexts/AuthContext.tsx","useAuth")},9891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};