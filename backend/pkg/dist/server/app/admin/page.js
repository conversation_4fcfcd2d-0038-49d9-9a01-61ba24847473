(()=>{var e={};e.id=698,e.ids=[698],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},940:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},981:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(687),a=t(3210),i=t(8383),l=t(4570),n=t(8749),d=t(1312),c=t(940),x=t(9891);let m=(0,t(2688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);function o(){let[e,s]=(0,a.useState)(!0),[t,o]=(0,a.useState)({totalUsers:0,activeUsers:0,adminUsers:0,regularUsers:0}),[p,h]=(0,a.useState)([]);return e?(0,r.jsx)(l.A,{requireAdmin:!0,children:(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,r.jsx)(l.A,{requireAdmin:!0,children:(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Sistem y\xf6netimi ve kullanıcı istatistikleri"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Kullanıcı"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.totalUsers})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Aktif Kullanıcı"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.activeUsers})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Admin"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.adminUsers})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m,{className:"h-8 w-8 text-orange-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Normal Kullanıcı"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.regularUsers})]})]})})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son Kullanıcılar"})}),(0,r.jsx)(n.Wu,{children:0===p.length?(0,r.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz kullanıcı bulunmuyor"}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kullanıcı Adı"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rol"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Oluşturulma Tarihi"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.username}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"admin"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:"admin"===e.role?"Admin":"Kullanıcı"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_active?"Aktif":"Pasif"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.created_at})]},e.id))})]})})})]})]})})})}t(535)},1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/admin/page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4952:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7012:(e,s,t)=>{Promise.resolve().then(t.bind(t,981))},7156:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},8749:(e,s,t)=>{"use strict";t.d(s,{Wu:()=>d,Zp:()=>l,aR:()=>n});var r=t(687),a=t(3210),i=t(9384);let l=(0,a.forwardRef)(({className:e,children:s,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...t,children:s}));l.displayName="Card";let n=(0,a.forwardRef)(({className:e,children:s,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-b border-gray-200",e),...t,children:s}));n.displayName="CardHeader",(0,a.forwardRef)(({className:e,children:s,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,i.A)("text-lg font-semibold text-gray-900",e),...t,children:s})).displayName="CardTitle",(0,a.forwardRef)(({className:e,children:s,...t},a)=>(0,r.jsx)("p",{ref:a,className:(0,i.A)("text-sm text-gray-600 mt-1",e),...t,children:s})).displayName="CardDescription";let d=(0,a.forwardRef)(({className:e,children:s,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4",e),...t,children:s}));d.displayName="CardContent",(0,a.forwardRef)(({className:e,children:s,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...t,children:s})).displayName="CardFooter"},8863:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>x,routeModule:()=>o,tree:()=>c});var r=t(5239),a=t(8088),i=t(8170),l=t.n(i),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"/Users/<USER>/nocytech/business-management/frontend/src/app/admin/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["/Users/<USER>/nocytech/business-management/frontend/src/app/admin/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},o=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[169,798,58],()=>t(8863));module.exports=r})();