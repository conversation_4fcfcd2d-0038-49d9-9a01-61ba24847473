(()=>{var e={};e.id=913,e.ids=[913],e.modules={228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},299:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(687),a=r(3210),i=r(6189),l=r(8383),n=r(4570),d=r(2643),c=r(8749),m=r(8559),x=r(9891),o=r(8869),u=r(940),h=r(228),p=r(535);function g(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),r=e.id,[g,j]=(0,a.useState)(null),[y,f]=(0,a.useState)(!0),[v,b]=(0,a.useState)(null);return((0,a.useCallback)(async()=>{try{f(!0),b(null);let e=await p.y.getUserById(r);if(!e)return void b("Kullanıcı bulunamadı.");j(e)}catch(e){console.error("Error loading user:",e),b("Kullanıcı bilgileri y\xfcklenirken bir hata oluştu.")}finally{f(!1)}},[r]),y)?(0,t.jsx)(n.A,{requireAdmin:!0,children:(0,t.jsx)(l.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):v||!g?(0,t.jsx)(n.A,{requireAdmin:!0,children:(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-red-600 text-lg mb-4",children:v||"Kullanıcı bulunamadı"}),(0,t.jsxs)(d.A,{onClick:()=>s.push("/admin/users"),variant:"primary",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Kullanıcı Listesine D\xf6n"]})]})})}):(0,t.jsx)(n.A,{requireAdmin:!0,children:(0,t.jsx)(l.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(d.A,{onClick:()=>s.push("/admin/users"),variant:"secondary",size:"sm",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kullanıcı Detayları"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[g.username," kullanıcısının bilgileri"]})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(c.Zp,{className:"lg:col-span-2",children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Temel Bilgiler"})}),(0,t.jsxs)(c.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:`h-16 w-16 rounded-full flex items-center justify-center ${"admin"===g.role?"bg-purple-100":"bg-blue-100"}`,children:"admin"===g.role?(0,t.jsx)(x.A,{className:`h-8 w-8 ${"admin"===g.role?"text-purple-600":"text-blue-600"}`}):(0,t.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:g.username}),(0,t.jsx)("p",{className:"text-gray-600",children:"admin"===g.role?"Sistem Y\xf6neticisi":"Kullanıcı"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-500",children:"Kullanıcı Adı"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-900",children:g.username})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-500",children:"Rol"}),(0,t.jsx)("span",{className:`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"admin"===g.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:"admin"===g.role?"Admin":"Kullanıcı"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-500",children:"Durum"}),(0,t.jsx)("span",{className:`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${g.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:g.is_active?"Aktif":"Pasif"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-500",children:"Kullanıcı ID"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-900 font-mono",children:g.id})]})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Durum Bilgileri"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(u.A,{className:`h-5 w-5 ${g.is_active?"text-green-600":"text-red-600"}`}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:g.is_active?"Aktif Kullanıcı":"Pasif Kullanıcı"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:g.is_active?"Sisteme giriş yapabilir":"Sisteme giriş yapamaz"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(x.A,{className:`h-5 w-5 ${"admin"===g.role?"text-purple-600":"text-blue-600"}`}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"admin"===g.role?"Y\xf6netici Yetkisi":"Kullanıcı Yetkisi"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"admin"===g.role?"T\xfcm sistem \xf6zelliklerine erişim":"Sınırlı sistem erişimi"})]})]})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Zaman Bilgileri"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Oluşturulma Tarihi"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:g.created_at})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Son G\xfcncelleme"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:g.updated_at})]})]})]})})]})]})})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},940:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},1189:(e,s,r)=>{Promise.resolve().then(r.bind(r,299))},1429:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/admin/users/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/admin/users/[id]/page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4952:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7621:(e,s,r)=>{Promise.resolve().then(r.bind(r,1429))},8559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>d,Zp:()=>l,aR:()=>n});var t=r(687),a=r(3210),i=r(9384);let l=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...r,children:s}));l.displayName="Card";let n=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-b border-gray-200",e),...r,children:s}));n.displayName="CardHeader",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("h3",{ref:a,className:(0,i.A)("text-lg font-semibold text-gray-900",e),...r,children:s})).displayName="CardTitle",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("p",{ref:a,className:(0,i.A)("text-sm text-gray-600 mt-1",e),...r,children:s})).displayName="CardDescription";let d=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4",e),...r,children:s}));d.displayName="CardContent",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...r,children:s})).displayName="CardFooter"},8807:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>m,routeModule:()=>o,tree:()=>c});var t=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["admin",{children:["users",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1429)),"/Users/<USER>/nocytech/business-management/frontend/src/app/admin/users/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=["/Users/<USER>/nocytech/business-management/frontend/src/app/admin/users/[id]/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},o=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/users/[id]/page",pathname:"/admin/users/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8869:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[169,798,58],()=>r(8807));module.exports=t})();