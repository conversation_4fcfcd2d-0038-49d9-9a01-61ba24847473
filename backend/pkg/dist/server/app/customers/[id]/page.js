(()=>{var e={};e.id=358,e.ids=[358],e.modules={228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},902:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/customers/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/customers/[id]/page.tsx","default")},2977:(e,s,r)=>{"use strict";r.d(s,{A7:()=>h,Ay:()=>o,JH:()=>m,Qn:()=>x});var t=r(687),a=r(6189),i=r(646),l=r(8749),n=r(2643),c=r(8559),d=r(3861);function o({title:e,subtitle:s,loading:r=!1,error:o=null,onBack:m,backUrl:x,children:h,actions:u}){let p=(0,a.useRouter)(),g=()=>{m?m():x?p.push(x):p.back()};return r?(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):o?(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(n.A,{variant:"secondary",onClick:g,className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Geri"]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-red-600 text-lg font-medium mb-2",children:"Hata"}),(0,t.jsx)("div",{className:"text-gray-600",children:o})]})})]})}):(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(n.A,{variant:"secondary",onClick:g,className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e})]}),s&&(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:s})]})]}),u&&(0,t.jsx)("div",{className:"flex items-center space-x-4",children:u})]}),h]})})}function m({title:e,children:s,className:r=""}){return(0,t.jsxs)(l.Zp,{className:r,children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:e})}),(0,t.jsx)(l.Wu,{children:s})]})}function x({label:e,value:s,className:r=""}){return(0,t.jsxs)("div",{className:`flex justify-between py-2 border-b border-gray-100 last:border-b-0 ${r}`,children:[(0,t.jsxs)("span",{className:"text-gray-600 font-medium",children:[e,":"]}),(0,t.jsx)("span",{className:"text-gray-900",children:s})]})}function h({children:e,columns:s=2,className:r=""}){return(0,t.jsx)("div",{className:`grid ${{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}[s]} gap-6 ${r}`,children:e})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3268:(e,s,r)=>{Promise.resolve().then(r.bind(r,902))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},4703:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var t=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let d={children:["",{children:["customers",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,902)),"/Users/<USER>/nocytech/business-management/frontend/src/app/customers/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/nocytech/business-management/frontend/src/app/customers/[id]/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customers/[id]/page",pathname:"/customers/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4780:(e,s,r)=>{"use strict";function t(e){if(!e)return"-";try{let s=new Date(e);if(isNaN(s.getTime()))return e;return s.toLocaleString("tr-TR",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(s){return console.error("Error formatting date:",s),e}}function a(e){if(!e)return"";let s=e.replace(/\D/g,"");return 11===s.length&&s.startsWith("0")?`${s.slice(0,4)} ${s.slice(4,7)} ${s.slice(7,9)} ${s.slice(9,11)}`:10===s.length?`0${s.slice(0,3)} ${s.slice(3,6)} ${s.slice(6,8)} ${s.slice(8,10)}`:e}function i(e){if(!e)return"";let s=e.replace(/\D/g,"");return 11===s.length?`${s.slice(0,3)} ${s.slice(3,6)} ${s.slice(6,9)} ${s.slice(9,11)}`:e}r.d(s,{NZ:()=>i,Yq:()=>t,qH:()=>a})},6004:(e,s,r)=>{Promise.resolve().then(r.bind(r,7076))},6345:(e,s,r)=>{"use strict";r.d(s,{m:()=>a});var t=r(2185);let a={getAll:async()=>(await t.u.get("/customers")).data||[],async getPaginated(e){let s=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get(`/customers/paginated?${s}`)},async getById(e){try{return(await t.u.get(`/customers/${e}`)).data||null}catch(e){return console.error("Error fetching customer:",e),null}},async create(e){await t.u.post("/customers",e)},async update(e,s){await t.u.put(`/customers/${e}`,s)},async delete(e){await t.u.delete(`/customers/${e}`)},async searchByTC(e){if(!e||e.length<2)return[];try{return(await t.u.get(`/customers/search/${e}`)).data||[]}catch(e){return console.error("Error searching customers:",e),[]}}}},7076:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>y});var t=r(687),a=r(3210),i=r(6189),l=r(2977),n=r(2643),c=r(6345),d=r(4780),o=r(3143),m=r(8233),x=r(1312),h=r(5778),u=r(8340),p=r(7992),g=r(228);function y(){let e=(0,i.useParams)().id,[s,r]=(0,a.useState)(null),[y,j]=(0,a.useState)(!0),[f,v]=(0,a.useState)(null);(0,a.useCallback)(async()=>{try{j(!0),v(null);let s=await c.m.getById(e);if(!s)return void v("M\xfcşteri bulunamadı.");r(s)}catch(e){console.error("Error loading customer:",e),v("M\xfcşteri bilgileri y\xfcklenirken bir hata oluştu.")}finally{j(!1)}},[e]);let N=s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,t.jsxs)(n.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,t.jsx)(l.Ay,{title:s?.name||"M\xfcşteri Detayı",subtitle:"M\xfcşteri bilgilerini g\xf6r\xfcnt\xfcleyin",loading:y,error:f,backUrl:"/customers",actions:N,children:s&&(0,t.jsxs)(l.A7,{columns:2,children:[(0,t.jsx)(l.JH,{title:"Kişisel Bilgiler",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(l.Qn,{label:"Ad Soyad",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:s.name})]})}),(0,t.jsx)(l.Qn,{label:"TC Kimlik No",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-2 py-1 rounded",children:(0,d.NZ)(s.tc)})]})}),(0,t.jsx)(l.Qn,{label:"Telefon",value:s.phone?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,t.jsx)("a",{href:`tel:${s.phone}`,className:"text-purple-600 hover:text-purple-800 font-medium",children:(0,d.qH)(s.phone)})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})})]})}),(0,t.jsx)(l.JH,{title:"İletişim Bilgileri",children:(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsx)(l.Qn,{label:"Adres",value:s.address?(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm leading-relaxed",children:s.address})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Adres belirtilmemiş"})})})}),(0,t.jsx)(l.JH,{title:"Kayıt Bilgileri",className:"md:col-span-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(l.Qn,{label:"Kayıt Tarihi",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{children:(0,d.Yq)(s.created_at)})]})}),(0,t.jsx)(l.Qn,{label:"Son G\xfcncelleme",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{children:(0,d.Yq)(s.updated_at)})]})})]})}),(0,t.jsx)(l.JH,{title:"M\xfcşteri \xd6zeti",className:"md:col-span-2",children:(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:"M\xfcşteri Bilgi Kartı"})]}),(0,t.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Ad Soyad:"})," ",s.name]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"TC:"})," ",(0,d.NZ)(s.tc)]}),s.phone&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Telefon:"})," ",(0,d.qH)(s.phone)]}),s.address&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Adres:"})," ",s.address]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Kayıt:"})," ",(0,d.Yq)(s.created_at)]})]})]})})]})})}},7992:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8233:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8340:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},8559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>c,Zp:()=>l,aR:()=>n});var t=r(687),a=r(3210),i=r(9384);let l=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...r,children:s}));l.displayName="Card";let n=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-b border-gray-200",e),...r,children:s}));n.displayName="CardHeader",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("h3",{ref:a,className:(0,i.A)("text-lg font-semibold text-gray-900",e),...r,children:s})).displayName="CardTitle",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("p",{ref:a,className:(0,i.A)("text-sm text-gray-600 mt-1",e),...r,children:s})).displayName="CardDescription";let c=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4",e),...r,children:s}));c.displayName="CardContent",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...r,children:s})).displayName="CardFooter"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[169,798,480],()=>r(4703));module.exports=t})();