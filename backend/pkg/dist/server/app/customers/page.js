(()=>{var e={};e.id=812,e.ids=[812],e.modules={506:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/customers/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/customers/page.tsx","default")},615:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>o});var s=t(5239),a=t(8088),n=t(8170),i=t.n(n),l=t(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(r,c);let o={children:["",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,506)),"/Users/<USER>/nocytech/business-management/frontend/src/app/customers/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/nocytech/business-management/frontend/src/app/customers/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customers/page",pathname:"/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2094:(e,r,t)=>{Promise.resolve().then(t.bind(t,506))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},5142:(e,r,t)=>{Promise.resolve().then(t.bind(t,7148))},6345:(e,r,t)=>{"use strict";t.d(r,{m:()=>a});var s=t(2185);let a={getAll:async()=>(await s.u.get("/customers")).data||[],async getPaginated(e){let r=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/customers/paginated?${r}`)},async getById(e){try{return(await s.u.get(`/customers/${e}`)).data||null}catch(e){return console.error("Error fetching customer:",e),null}},async create(e){await s.u.post("/customers",e)},async update(e,r){await s.u.put(`/customers/${e}`,r)},async delete(e){await s.u.delete(`/customers/${e}`)},async searchByTC(e){if(!e||e.length<2)return[];try{return(await s.u.get(`/customers/search/${e}`)).data||[]}catch(e){return console.error("Error searching customers:",e),[]}}}},7148:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(687),a=t(3210),n=t(6189),i=t(646),l=t(2643),c=t(1907),o=t(7576),d=t(4926),u=t(1312),p=t(3861),m=t(3143),h=t(8233),g=t(6474),x=t(9270),y=t(6345);function v(){let e=(0,n.useRouter)(),[r,t]=(0,a.useState)([]),[v,f]=(0,a.useState)(!0),[j,A]=(0,a.useState)(!1),[b,C]=(0,a.useState)(!1),[k,N]=(0,a.useState)(null),[w,_]=(0,a.useState)({page:1,per_page:10}),[P,M]=(0,a.useState)(""),[S,q]=(0,a.useState)(""),[z,E]=(0,a.useState)({name:"",phone:"",tc:"",address:""}),T=async()=>{try{f(!0);let e=await y.m.getAll();t(e)}catch(e){console.error("Error loading customers:",e)}finally{f(!1)}},$=()=>{M(S),_(e=>({...e,page:1}))},K=e=>{q(e)},U=async()=>{try{await y.m.create(z),A(!1),E({name:"",phone:"",tc:"",address:""}),T()}catch(e){console.error("Error creating customer:",e),alert("M\xfcşteri oluşturulurken hata oluştu: "+e.message)}},G=e=>{N(e),E({name:e.name,phone:e.phone,tc:e.tc,address:e.address}),C(!0)},O=async()=>{if(k)try{let e={name:z.name,phone:z.phone,tc:z.tc};await y.m.update(k.id,e),C(!1),N(null),T()}catch(e){console.error("Error updating customer:",e),alert("M\xfcşteri g\xfcncellenirken hata oluştu: "+e.message)}},R=async e=>{if(confirm("Bu m\xfcşteriyi silmek istediğinizden emin misiniz?"))try{await y.m.delete(e),T()}catch(e){console.error("Error deleting customer:",e),alert("M\xfcşteri silinirken hata oluştu: "+e.message)}},D=e=>{let[r,t]=e.split(" "),[s,a,n]=r.split("-"),[i,l]=t.split(":");return`${n}.${a}.${s} ${i}:${l}`},B=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),I=r.filter(e=>{if(!P)return!0;let r=B(P);return B(e.name).includes(r)||B(e.phone).includes(r)||B(e.tc).includes(r)||e.address&&B(e.address).includes(r)}),Y=[{key:"name",header:"M\xfcşteri",render:e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name})]})},{key:"tc",header:"TC Kimlik",render:e=>e.tc},{key:"phone",header:"Telefon",render:e=>e.phone||"-"},{key:"address",header:"Adres",render:e=>e.address||"-"},{key:"created_at",header:"Kayıt Tarihi",render:e=>D(e.created_at)},{key:"actions",header:"İşlemler",className:"text-right",render:r=>(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(l.A,{size:"sm",variant:"success",onClick:()=>e.push(`/customers/${r.id}`),title:"Detay",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.A,{size:"sm",variant:"info",onClick:()=>G(r),title:"D\xfczenle",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.A,{size:"sm",variant:"danger",onClick:()=>R(r.id),title:"Sil",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]})}];return v?(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,s.jsxs)(i.A,{children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"M\xfcşteriler"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Kayıtlı m\xfcşterileri g\xf6r\xfcnt\xfcleyin ve y\xf6netin"})]}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(l.A,{onClick:()=>A(!0),children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Yeni M\xfcşteri"]})})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"M\xfcşteri adı, telefon, TC veya adres ara...",value:S,onChange:e=>K(e.target.value),onKeyDown:e=>"Enter"===e.key&&$(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)(l.A,{onClick:$,variant:"primary",className:"px-4 py-2",children:"Ara"}),P&&(0,s.jsx)(l.A,{onClick:()=>{M(""),q(""),_(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),P&&(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[I.length,' m\xfcşteri g\xf6steriliyor • "',P,'" araması']})]}),(0,s.jsx)(d.A,{columns:Y,data:I,pagination:{page:w.page,per_page:w.per_page,total:I.length,total_pages:Math.ceil(I.length/w.per_page),has_next:w.page<Math.ceil(I.length/w.per_page),has_prev:w.page>1},onPageChange:e=>{_(r=>({...r,page:e}))},onPerPageChange:e=>{_({page:1,per_page:e})},loading:v,emptyMessage:"Hen\xfcz m\xfcşteri kaydı bulunmuyor",emptyIcon:(0,s.jsx)(u.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0})]}),(0,s.jsx)(o.A,{isOpen:j,onClose:()=>{A(!1),E({name:"",phone:"",tc:"",address:""})},title:"Yeni M\xfcşteri Oluştur",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c.A,{label:"M\xfcşteri Adı",value:z.name,onChange:e=>E(r=>({...r,name:e.target.value})),required:!0}),(0,s.jsx)(c.A,{label:"Telefon",value:z.phone,onChange:e=>E(r=>({...r,phone:e.target.value}))}),(0,s.jsx)(c.A,{label:"TC Kimlik No",value:z.tc,onChange:e=>E(r=>({...r,tc:e.target.value})),required:!0}),(0,s.jsx)(c.A,{label:"Adres",value:z.address,onChange:e=>E(r=>({...r,address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(l.A,{variant:"secondary",onClick:()=>{A(!1),E({name:"",phone:"",tc:"",address:""})},children:"İptal"}),(0,s.jsx)(l.A,{onClick:U,children:"Oluştur"})]})]})}),(0,s.jsx)(o.A,{isOpen:b,onClose:()=>{C(!1),N(null)},title:"M\xfcşteri D\xfczenle",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c.A,{label:"M\xfcşteri Adı",value:z.name,onChange:e=>E(r=>({...r,name:e.target.value})),required:!0}),(0,s.jsx)(c.A,{label:"Telefon",value:z.phone,onChange:e=>E(r=>({...r,phone:e.target.value}))}),(0,s.jsx)(c.A,{label:"TC Kimlik No",value:z.tc,onChange:e=>E(r=>({...r,tc:e.target.value})),required:!0}),(0,s.jsx)(c.A,{label:"Adres",value:z.address,onChange:e=>E(r=>({...r,address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)(l.A,{variant:"secondary",onClick:()=>{C(!1),N(null)},children:"İptal"}),(0,s.jsx)(l.A,{onClick:O,children:"G\xfcncelle"})]})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[169,798,71,480,248],()=>t(615));module.exports=s})();