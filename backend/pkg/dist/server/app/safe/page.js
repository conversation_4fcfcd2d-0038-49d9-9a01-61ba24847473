(()=>{var e={};e.id=750,e.ids=[750],e.modules={777:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/safe/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/safe/page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1262:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>g});var t=s(687),r=s(3210),n=s(646),l=s(8749),i=s(2643),c=s(7576),d=s(1907),o=s(6474),x=s(6476),m=s(5541);let u=(0,s(2688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var h=s(3143),p=s(8233),y=s(7766);function g(){let[e,a]=(0,r.useState)([]),[s,g]=(0,r.useState)(0),[f,j]=(0,r.useState)(!0),[v,b]=(0,r.useState)(!1),[N,w]=(0,r.useState)(!1),[k,A]=(0,r.useState)(!1),[C,T]=(0,r.useState)(!1),[P,S]=(0,r.useState)(null),[R,_]=(0,r.useState)(null),[z,E]=(0,r.useState)({amount:0}),[M,F]=(0,r.useState)(0),K=async()=>{try{j(!0);let[e,s]=await Promise.all([y.x.getAll(),y.x.getTotalAmount()]);a(e),g(s)}catch(e){console.error("Error loading data:",e)}finally{j(!1)}},q=async()=>{try{await y.x.create(z),b(!1),E({amount:0}),K()}catch(e){console.error("Error creating safe:",e)}},G=async()=>{if(P)try{let e={amount:z.amount};await y.x.update(P.id,e),w(!1),S(null),K()}catch(e){console.error("Error updating safe:",e)}},L=async e=>{if(confirm("Bu kasayı silmek istediğinizden emin misiniz?"))try{await y.x.delete(e),K()}catch(e){console.error("Error deleting safe:",e)}},O=async()=>{if(R)try{await y.x.addMoney(R.id,{amount:M}),A(!1),_(null),F(0),K()}catch(e){console.error("Error adding money:",e)}},B=async()=>{if(R)try{await y.x.withdrawMoney(R.id,{amount:M}),T(!1),_(null),F(0),K()}catch(e){console.error("Error withdrawing money:",e)}},$=e=>{S(e),E({amount:e.amount}),w(!0)},D=e=>{_(e),F(0),A(!0)},U=e=>{_(e),F(0),T(!0)};return f?(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,t.jsx)(n.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kasa Y\xf6netimi"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Kasalarınızı ve para akışınızı y\xf6netin"})]}),(0,t.jsxs)(i.A,{onClick:()=>b(!0),children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Yeni Kasa"]})]}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-2",children:(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Toplam Bakiye"})}),(0,t.jsxs)("p",{className:"text-4xl font-bold text-green-600",children:["₺",s.toLocaleString("tr-TR")]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[e.length," kasa"]})]})})})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsxs)("h3",{className:"font-medium text-gray-900",children:["Kasa #",e.id.slice(-8)]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.A,{size:"sm",variant:"success",onClick:()=>D(e),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"danger",onClick:()=>U(e),children:(0,t.jsx)(u,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"secondary",onClick:()=>$(e),children:(0,t.jsx)(h.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"danger",onClick:()=>L(e.id),children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-3xl font-bold text-green-600",children:["₺",e.amount.toLocaleString("tr-TR")]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Mevcut Bakiye"})]}),(0,t.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Oluşturulma:"}),(0,t.jsx)("span",{children:new Date(e.created_at).toLocaleDateString("tr-TR")})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Son G\xfcncelleme:"}),(0,t.jsx)("span",{children:new Date(e.updated_at).toLocaleDateString("tr-TR")})]})]})]})})]},e.id))}),0===e.length&&(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,t.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hen\xfcz kasa yok"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"İlk kasanızı oluşturarak başlayın"}),(0,t.jsxs)(i.A,{onClick:()=>b(!0),children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Yeni Kasa Oluştur"]})]})}),(0,t.jsx)(c.A,{isOpen:v,onClose:()=>b(!1),title:"Yeni Kasa Oluştur",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(d.A,{label:"Başlangı\xe7 Tutarı",type:"number",value:z.amount,onChange:e=>E({...z,amount:parseFloat(e.target.value)||0}),placeholder:"0.00",helperText:"Kasanın başlangı\xe7 bakiyesini girin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>b(!1),children:"İptal"}),(0,t.jsx)(i.A,{onClick:q,children:"Kasa Oluştur"})]})]})}),(0,t.jsx)(c.A,{isOpen:N,onClose:()=>w(!1),title:"Kasa D\xfczenle",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(d.A,{label:"Bakiye",type:"number",value:z.amount,onChange:e=>E({...z,amount:parseFloat(e.target.value)||0}),placeholder:"0.00",helperText:"Kasanın mevcut bakiyesini g\xfcncelleyin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>w(!1),children:"İptal"}),(0,t.jsx)(i.A,{onClick:G,children:"G\xfcncelle"})]})]})}),(0,t.jsx)(c.A,{isOpen:k,onClose:()=>A(!1),title:"Para Ekle",children:(0,t.jsx)("div",{className:"space-y-4",children:R&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900",children:["Kasa #",R.id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Mevcut Bakiye: ₺",R.amount.toLocaleString("tr-TR")]})]}),(0,t.jsx)(d.A,{label:"Eklenecek Tutar",type:"number",value:M,onChange:e=>F(parseFloat(e.target.value)||0),placeholder:"0.00",helperText:"Kasaya eklemek istediğiniz tutarı girin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>A(!1),children:"İptal"}),(0,t.jsx)(i.A,{variant:"success",onClick:O,children:"Para Ekle"})]})]})})}),(0,t.jsx)(c.A,{isOpen:C,onClose:()=>T(!1),title:"Para \xc7ek",children:(0,t.jsx)("div",{className:"space-y-4",children:R&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900",children:["Kasa #",R.id.slice(-8)]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Mevcut Bakiye: ₺",R.amount.toLocaleString("tr-TR")]})]}),(0,t.jsx)(d.A,{label:"\xc7ekilecek Tutar",type:"number",value:M,onChange:e=>F(parseFloat(e.target.value)||0),placeholder:"0.00",helperText:`Maksimum: ₺${R.amount.toLocaleString("tr-TR")}`}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>T(!1),children:"İptal"}),(0,t.jsx)(i.A,{variant:"danger",onClick:B,children:"Para \xc7ek"})]})]})})})]})})}},1907:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});var t=s(687),r=s(3210),n=s(9384);let l=(0,r.forwardRef)(({className:e,label:a,error:s,helperText:r,type:l="text",...i},c)=>(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:a}),(0,t.jsx)("input",{type:l,className:(0,n.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",s&&"border-red-300 focus:ring-red-500 focus:border-red-500",e),ref:c,...i}),s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s}),r&&!s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r})]}));l.displayName="Input";let i=l},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5541:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},5584:(e,a,s)=>{Promise.resolve().then(s.bind(s,777))},7576:(e,a,s)=>{"use strict";s.d(a,{A:()=>d});var t=s(687),r=s(3210),n=s(3516),l=s(8908),i=s(1860),c=s(2643);function d({isOpen:e,onClose:a,title:s,children:d,size:o="md"}){return(0,t.jsx)(n.e,{appear:!0,show:e,as:r.Fragment,children:(0,t.jsxs)(l.lG,{as:"div",className:"relative z-50",onClose:a,children:[(0,t.jsx)(n.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,t.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,t.jsx)(n.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,t.jsxs)(l.lG.Panel,{className:`w-full ${{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[o]} transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,t.jsx)(l.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:s}),(0,t.jsx)(c.A,{variant:"secondary",size:"sm",onClick:a,className:"p-2",children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"px-6 py-4",children:d})]})})})})]})})}},7766:(e,a,s)=>{"use strict";s.d(a,{x:()=>r});var t=s(2185);let r={getAll:async()=>(await t.u.get("/safes")).data||[],async getById(e){let a=await t.u.get(`/safes/${e}`);if(!a.data)throw Error("Safe not found");return a.data},async create(e){await t.u.post("/safes",e)},async update(e,a){await t.u.put(`/safes/${e}`,a)},async delete(e){await t.u.delete(`/safes/${e}`)},async addMoney(e,a){await t.u.post(`/safes/${e}/add-money`,a)},async withdrawMoney(e,a){await t.u.post(`/safes/${e}/withdraw-money`,a)},getTotalAmount:async()=>(await t.u.get("/safes/total")).total_amount||0}},8749:(e,a,s)=>{"use strict";s.d(a,{Wu:()=>c,Zp:()=>l,aR:()=>i});var t=s(687),r=s(3210),n=s(9384);let l=(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...s,children:a}));l.displayName="Card";let i=(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.A)("px-6 py-4 border-b border-gray-200",e),...s,children:a}));i.displayName="CardHeader",(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,n.A)("text-lg font-semibold text-gray-900",e),...s,children:a})).displayName="CardTitle",(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,n.A)("text-sm text-gray-600 mt-1",e),...s,children:a})).displayName="CardDescription";let c=(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.A)("px-6 py-4",e),...s,children:a}));c.displayName="CardContent",(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...s,children:a})).displayName="CardFooter"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9136:(e,a,s)=>{Promise.resolve().then(s.bind(s,1262))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9567:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var t=s(5239),r=s(8088),n=s(8170),l=s.n(n),i=s(893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(a,c);let d={children:["",{children:["safe",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,777)),"/Users/<USER>/nocytech/business-management/frontend/src/app/safe/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/nocytech/business-management/frontend/src/app/safe/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/safe/page",pathname:"/safe",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[169,798,71,480],()=>s(9567));module.exports=t})();