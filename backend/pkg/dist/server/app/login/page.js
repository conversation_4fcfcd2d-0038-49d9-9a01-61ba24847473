(()=>{var e={};e.id=520,e.ids=[520],e.modules={68:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},87:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),a=r(8088),n=r(8170),i=r.n(n),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4934)),"/Users/<USER>/nocytech/business-management/frontend/src/app/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/nocytech/business-management/frontend/src/app/login/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},464:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(687),a=r(3210),n=r(6189),i=r(3213),o=r(2643),l=r(1907),d=r(8749);let c=(0,r(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var u=r(3613),m=r(8869);function h(){let e=(0,n.useRouter)(),{login:t}=(0,i.A)(),[r,h]=(0,a.useState)({username:"",password:""}),[p,g]=(0,a.useState)(!1),[f,x]=(0,a.useState)(""),y=async s=>{s.preventDefault(),g(!0),x("");try{await t(r),e.push("/")}catch(e){console.error("Login error:",e),x("Kullanıcı adı veya şifre hatalı")}finally{g(!1)}},v=(e,t)=>{h(r=>({...r,[e]:t}))};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,s.jsx)(c,{className:"h-6 w-6 text-blue-600"})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Giriş Yap"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"İş Y\xf6netim Sistemine hoş geldiniz"})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Hesabınıza giriş yapın"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-red-400"}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm text-red-800",children:f})})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.A,{label:"Kullanıcı Adı",type:"text",value:r.username,onChange:e=>v("username",e.target.value),placeholder:"Kullanıcı adınızı girin",required:!0,className:"relative"}),(0,s.jsx)(m.A,{className:"absolute left-3 top-9 h-5 w-5 text-gray-400"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.A,{label:"Şifre",type:"password",value:r.password,onChange:e=>v("password",e.target.value),placeholder:"Şifrenizi girin",required:!0,className:"relative"}),(0,s.jsx)(c,{className:"absolute left-3 top-9 h-5 w-5 text-gray-400"})]}),(0,s.jsx)("div",{children:(0,s.jsx)(o.A,{type:"submit",loading:p,className:"w-full",variant:"primary",children:p?"Giriş yapılıyor...":"Giriş Yap"})})]})})]})]})})}},535:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});var s=r(2185);let a={async login(e){let t=await s.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await s.u.post("/users",e)},getAllUsers:async()=>(await s.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/users/paginated?${t}`)},async getUserById(e){let t=await s.u.get(`/users/${e}`);if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await s.u.put(`/users/${e}`,t)},async deleteUser(e){await s.u.delete(`/users/${e}`)}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1907:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(687),a=r(3210),n=r(9384);let i=(0,a.forwardRef)(({className:e,label:t,error:r,helperText:a,type:i="text",...o},l)=>(0,s.jsxs)("div",{className:"w-full",children:[t&&(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:t}),(0,s.jsx)("input",{type:i,className:(0,n.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",r&&"border-red-300 focus:ring-red-500 focus:border-red-500",e),ref:l,...o}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r}),a&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a})]}));i.displayName="Input";let o=i},2185:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});let s=process.env.NEXT_PUBLIC_API_URL||"http://localhost:5555/api/v1";class a{constructor(e){this.baseURL=e}async request(e,t={}){let r=`${this.baseURL}${e}`,s=localStorage.getItem("auth_token"),a={headers:{"Content-Type":"application/json",...s&&{Authorization:`Bearer ${s}`},...t.headers},...t};try{let e=await fetch(r,a);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let r=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(([t,r])=>{null!=r&&e.append(t,String(r))});let s=e.toString();s&&(r+=`?${s}`)}return this.request(r,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}}let n=new a(s)},2643:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(687),a=r(3210),n=r(9384);let i=(0,a.forwardRef)(({className:e,variant:t="primary",size:r="md",loading:a,children:i,disabled:o,...l},d)=>(0,s.jsxs)("button",{className:(0,n.A)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500":"primary"===t,"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500":"secondary"===t,"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500":"danger"===t,"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500":"success"===t,"bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500":"warning"===t,"bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500":"info"===t,"px-3 py-1.5 text-sm":"sm"===r,"px-4 py-2 text-sm":"md"===r,"px-6 py-3 text-base":"lg"===r},e),disabled:o||a,ref:d,...l,children:[a&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]}));i.displayName="Button";let o=i},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:i,iconNode:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",n),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},l)=>(0,s.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,r),...n}));return r.displayName=i(e),r}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>o});var s=r(687),a=r(3210),n=r(535);let i=(0,a.createContext)(void 0);function o({children:e}){let[t,r]=(0,a.useState)(null),[o,l]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),[u,m]=(0,a.useState)(!0),h=async e=>{try{let t=await n.y.login(e);c(t.token),r(t.user),l(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},p={user:t,organization:o,token:d,isLoading:u,isAuthenticated:!!t&&!!d,isAdmin:t?.role==="admin",login:h,logout:()=>{c(null),r(null),l(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,s.jsx)(i.Provider,{value:p,children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3352:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},3613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},3965:(e,t,r)=>{Promise.resolve().then(r.bind(r,464))},4024:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(7413),a=r(2202),n=r.n(a),i=r(4988),o=r.n(i),l=r(9131);r(1135);let d={title:"Business Management System",description:"Comprehensive business management solution for inventory, debt, and financial tracking"};function c({children:e}){return(0,s.jsx)("html",{lang:"tr",children:(0,s.jsx)("body",{className:`${n().variable} ${o().variable} antialiased bg-gray-50 min-h-screen`,children:(0,s.jsx)(l.AuthProvider,{children:e})})})}},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/login/page.tsx","default")},6189:(e,t,r)=>{"use strict";var s=r(5773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},6916:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8749:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,Zp:()=>i,aR:()=>o});var s=r(687),a=r(3210),n=r(9384);let i=(0,a.forwardRef)(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...r,children:t}));i.displayName="Card";let o=(0,a.forwardRef)(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.A)("px-6 py-4 border-b border-gray-200",e),...r,children:t}));o.displayName="CardHeader",(0,a.forwardRef)(({className:e,children:t,...r},a)=>(0,s.jsx)("h3",{ref:a,className:(0,n.A)("text-lg font-semibold text-gray-900",e),...r,children:t})).displayName="CardTitle",(0,a.forwardRef)(({className:e,children:t,...r},a)=>(0,s.jsx)("p",{ref:a,className:(0,n.A)("text-sm text-gray-600 mt-1",e),...r,children:t})).displayName="CardDescription";let l=(0,a.forwardRef)(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.A)("px-6 py-4",e),...r,children:t}));l.displayName="CardContent",(0,a.forwardRef)(({className:e,children:t,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...r,children:t})).displayName="CardFooter"},8869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(2907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/contexts/AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/contexts/AuthContext.tsx","useAuth")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9384:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=function(){for(var e,t,r=0,s="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,s,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var n=t.length;for(r=0;r<n;r++)t[r]&&(s=e(t[r]))&&(a&&(a+=" "),a+=s)}else for(s in t)t[s]&&(a&&(a+=" "),a+=s);return a}(e))&&(s&&(s+=" "),s+=t);return s}},9490:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[169],()=>r(87));module.exports=s})();