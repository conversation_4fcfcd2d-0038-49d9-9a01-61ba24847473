(()=>{var e={};e.id=492,e.ids=[492],e.modules={68:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},535:(e,t,r)=>{"use strict";r.d(t,{y:()=>s});var n=r(2185);let s={async login(e){let t=await n.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await n.u.post("/users",e)},getAllUsers:async()=>(await n.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await n.u.get(`/users/paginated?${t}`)},async getUserById(e){let t=await n.u.get(`/users/${e}`);if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await n.u.put(`/users/${e}`,t)},async deleteUser(e){await n.u.delete(`/users/${e}`)}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2185:(e,t,r)=>{"use strict";r.d(t,{u:()=>o});let n=process.env.NEXT_PUBLIC_API_URL||"http://localhost:5555/api/v1";class s{constructor(e){this.baseURL=e}async request(e,t={}){let r=`${this.baseURL}${e}`,n=localStorage.getItem("auth_token"),s={headers:{"Content-Type":"application/json",...n&&{Authorization:`Bearer ${n}`},...t.headers},...t};try{let e=await fetch(r,s);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let r=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(([t,r])=>{null!=r&&e.append(t,String(r))});let n=e.toString();n&&(r+=`?${n}`)}return this.request(r,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}}let o=new s(n)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3213:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>a});var n=r(687),s=r(3210),o=r(535);let i=(0,s.createContext)(void 0);function a({children:e}){let[t,r]=(0,s.useState)(null),[a,l]=(0,s.useState)(null),[u,d]=(0,s.useState)(null),[c,h]=(0,s.useState)(!0),m=async e=>{try{let t=await o.y.login(e);d(t.token),r(t.user),l(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},p={user:t,organization:a,token:u,isLoading:c,isAuthenticated:!!t&&!!u,isAdmin:t?.role==="admin",login:m,logout:()=>{d(null),r(null),l(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,n.jsx)(i.Provider,{value:p,children:e})}function l(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3352:(e,t,r)=>{Promise.resolve().then(r.bind(r,9131))},3873:e=>{"use strict";e.exports=require("path")},4024:(e,t,r)=>{Promise.resolve().then(r.bind(r,3213))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>u});var n=r(7413),s=r(2202),o=r.n(s),i=r(4988),a=r.n(i),l=r(9131);r(1135);let u={title:"Business Management System",description:"Comprehensive business management solution for inventory, debt, and financial tracking"};function d({children:e}){return(0,n.jsx)("html",{lang:"tr",children:(0,n.jsx)("body",{className:`${o().variable} ${a().variable} antialiased bg-gray-50 min-h-screen`,children:(0,n.jsx)(l.AuthProvider,{children:e})})})}},6916:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},7957:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u});var n=r(5239),s=r(8088),o=r(8170),i=r.n(o),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],c={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var n=r(2907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/contexts/AuthContext.tsx","AuthProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/contexts/AuthContext.tsx","useAuth")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[169],()=>r(7957));module.exports=n})();