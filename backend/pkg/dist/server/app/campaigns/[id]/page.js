(()=>{var e={};e.id=516,e.ids=[516],e.modules={228:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1170:(e,a,s)=>{"use strict";s.d(a,{E:()=>r});var t=s(687);function r({children:e,variant:a="default",className:s=""}){return(0,t.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",danger:"bg-red-100 text-red-800",warning:"bg-yellow-100 text-yellow-800",info:"bg-blue-100 text-blue-800",secondary:"bg-gray-100 text-gray-600"}[a]} ${s}`,children:e})}s(3210)},1526:(e,a,s)=>{Promise.resolve().then(s.bind(s,9820))},2977:(e,a,s)=>{"use strict";s.d(a,{A7:()=>u,Ay:()=>m,JH:()=>o,Qn:()=>x});var t=s(687),r=s(6189),i=s(646),n=s(8749),l=s(2643),c=s(8559),d=s(3861);function m({title:e,subtitle:a,loading:s=!1,error:m=null,onBack:o,backUrl:x,children:u,actions:h}){let p=(0,r.useRouter)(),g=()=>{o?o():x?p.push(x):p.back()};return s?(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):m?(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(l.A,{variant:"secondary",onClick:g,className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Geri"]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-red-600 text-lg font-medium mb-2",children:"Hata"}),(0,t.jsx)("div",{className:"text-gray-600",children:m})]})})]})}):(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(l.A,{variant:"secondary",onClick:g,className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e})]}),a&&(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:a})]})]}),h&&(0,t.jsx)("div",{className:"flex items-center space-x-4",children:h})]}),u]})})}function o({title:e,children:a,className:s=""}){return(0,t.jsxs)(n.Zp,{className:s,children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:e})}),(0,t.jsx)(n.Wu,{children:a})]})}function x({label:e,value:a,className:s=""}){return(0,t.jsxs)("div",{className:`flex justify-between py-2 border-b border-gray-100 last:border-b-0 ${s}`,children:[(0,t.jsxs)("span",{className:"text-gray-600 font-medium",children:[e,":"]}),(0,t.jsx)("span",{className:"text-gray-900",children:a})]})}function u({children:e,columns:a=2,className:s=""}){return(0,t.jsx)("div",{className:`grid ${{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}[a]} gap-6 ${s}`,children:e})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3700:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>j});var t=s(687),r=s(3210),i=s(6189),n=s(2977),l=s(2643),c=s(1170),d=s(6899),m=s(4780),o=s(2688);let x=(0,o.A)("percent",[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]]);var u=s(3928),h=s(3143),p=s(8233),g=s(7360);let y=(0,o.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var f=s(228);function j(){let e=(0,i.useParams)().id,[a,s]=(0,r.useState)(null),[o,j]=(0,r.useState)(!0),[v,b]=(0,r.useState)(null);(0,r.useCallback)(async()=>{try{j(!0),b(null);let a=await d.A.getById(e);if(!a)return void b("Kampanya bulunamadı.");s(a)}catch(e){console.error("Error loading campaign:",e),b("Kampanya bilgileri y\xfcklenirken bir hata oluştu.")}finally{j(!1)}},[e]);let N=a&&new Date(a.start_date)<=new Date&&new Date(a.end_date)>=new Date,k=a&&new Date(a.end_date)<new Date,w=a&&new Date(a.start_date)>new Date,A=a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(l.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,t.jsxs)(l.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,t.jsx)(n.Ay,{title:a?.name||"Kampanya Detayı",subtitle:"Kampanya bilgilerini g\xf6r\xfcnt\xfcleyin",loading:o,error:v,backUrl:"/campaigns",actions:A,children:a&&(0,t.jsxs)(n.A7,{columns:2,children:[(0,t.jsx)(n.JH,{title:"Genel Bilgiler",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"Kampanya Adı",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:a.name})]})}),(0,t.jsx)(n.Qn,{label:"A\xe7ıklama",value:a.description||"-"}),(0,t.jsx)(n.Qn,{label:"Durum",value:N?(0,t.jsx)(c.E,{variant:"success",children:"Aktif"}):k?(0,t.jsx)(c.E,{variant:"danger",children:"S\xfcresi Dolmuş"}):w?(0,t.jsx)(c.E,{variant:"warning",children:"Yaklaşan"}):(0,t.jsx)(c.E,{variant:"secondary",children:"Bilinmiyor"})}),(0,t.jsx)(n.Qn,{label:"Cashback",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y,{className:`h-4 w-4 mr-1 ${a.is_cashback?"text-green-600":"text-gray-400"}`}),(0,t.jsx)("span",{className:a.is_cashback?"text-green-600 font-medium":"text-gray-500",children:a.is_cashback?"Aktif":"Pasif"})]})})]})}),(0,t.jsx)(n.JH,{title:"Tarih Bilgileri",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"Başlangı\xe7 Tarihi",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{children:(0,m.Yq)(a.start_date)})]})}),(0,t.jsx)(n.Qn,{label:"Bitiş Tarihi",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,t.jsx)("span",{children:(0,m.Yq)(a.end_date)})]})}),(0,t.jsx)(n.Qn,{label:"Oluşturulma",value:(0,m.Yq)(a.created_at)}),(0,t.jsx)(n.Qn,{label:"Son G\xfcncelleme",value:(0,m.Yq)(a.updated_at)})]})}),(0,t.jsxs)(n.JH,{title:"İndirim Bilgileri",className:"md:col-span-2",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"İndirim T\xfcr\xfc"}),(0,t.jsx)("div",{className:"text-lg font-semibold text-blue-600",children:"percentage"===a.discount_type?"Y\xfczde":"Sabit Tutar"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"İndirim Miktarı"}),(0,t.jsx)("div",{className:"text-lg font-semibold",children:a?"percentage"===a.discount_type?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x,{className:"h-4 w-4 text-blue-600 mr-1"}),(0,t.jsxs)("span",{className:"font-semibold text-blue-600",children:["%",a.discount_percent]})]}):(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,t.jsxs)("span",{className:"font-semibold text-green-600",children:["₺",a.discount_amount.toLocaleString("tr-TR")]})]}):"-"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Cashback Durumu"}),(0,t.jsx)("div",{className:`text-lg font-semibold ${a.is_cashback?"text-green-600":"text-gray-500"}`,children:a.is_cashback?"Aktif":"Pasif"})]})]}),a.is_cashback&&(0,t.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,t.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,t.jsx)("strong",{children:"Cashback A\xe7ıklaması:"})," Bu kampanyada verilen indirim tutarı ana organizasyon tarafından geri \xf6denir. \xdcr\xfcn\xfcn tam fiyatı kasaya eklenir, ancak satıcı ek manuel indirim yaparsa, kasa orijinal fiyat eksi manuel indirim tutarını alır."]})})]})]})})}},3861:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3928:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4734:(e,a,s)=>{Promise.resolve().then(s.bind(s,3700))},4780:(e,a,s)=>{"use strict";function t(e){if(!e)return"-";try{let a=new Date(e);if(isNaN(a.getTime()))return e;return a.toLocaleString("tr-TR",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(a){return console.error("Error formatting date:",a),e}}function r(e){if(!e)return"";let a=e.replace(/\D/g,"");return 11===a.length&&a.startsWith("0")?`${a.slice(0,4)} ${a.slice(4,7)} ${a.slice(7,9)} ${a.slice(9,11)}`:10===a.length?`0${a.slice(0,3)} ${a.slice(3,6)} ${a.slice(6,8)} ${a.slice(8,10)}`:e}function i(e){if(!e)return"";let a=e.replace(/\D/g,"");return 11===a.length?`${a.slice(0,3)} ${a.slice(3,6)} ${a.slice(6,9)} ${a.slice(9,11)}`:e}s.d(a,{NZ:()=>i,Yq:()=>t,qH:()=>r})},6899:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});var t=s(2185);let r={getAll:async()=>(await t.u.get("/campaigns")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get(`/campaigns/paginated?${a}`)},async getById(e){try{return(await t.u.get(`/campaigns/${e}`)).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await t.u.post("/campaigns",e)},async update(e,a){await t.u.put(`/campaigns/${e}`,a)},async delete(e){await t.u.delete(`/campaigns/${e}`)},getActive:async()=>(await t.u.get("/campaigns/active")).data||[]}},8233:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8559:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,a,s)=>{"use strict";s.d(a,{Wu:()=>c,Zp:()=>n,aR:()=>l});var t=s(687),r=s(3210),i=s(9384);let n=(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...s,children:a}));n.displayName="Card";let l=(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.A)("px-6 py-4 border-b border-gray-200",e),...s,children:a}));l.displayName="CardHeader",(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,i.A)("text-lg font-semibold text-gray-900",e),...s,children:a})).displayName="CardTitle",(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,i.A)("text-sm text-gray-600 mt-1",e),...s,children:a})).displayName="CardDescription";let c=(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.A)("px-6 py-4",e),...s,children:a}));c.displayName="CardContent",(0,r.forwardRef)(({className:e,children:a,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...s,children:a})).displayName="CardFooter"},9035:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>o,pages:()=>m,routeModule:()=>x,tree:()=>d});var t=s(5239),r=s(8088),i=s(8170),n=s.n(i),l=s(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(a,c);let d={children:["",{children:["campaigns",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9820)),"/Users/<USER>/nocytech/business-management/frontend/src/app/campaigns/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=["/Users/<USER>/nocytech/business-management/frontend/src/app/campaigns/[id]/page.tsx"],o={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/campaigns/[id]/page",pathname:"/campaigns/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9820:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/campaigns/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/campaigns/[id]/page.tsx","default")}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[169,798,480],()=>s(9035));module.exports=t})();