(()=>{var e={};e.id=379,e.ids=[379],e.modules={199:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>i.a,__next_app__:()=>g,pages:()=>d,routeModule:()=>p,tree:()=>c});var t=r(5239),s=r(8088),n=r(8170),i=r.n(n),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(a,o);let c={children:["",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8911)),"/Users/<USER>/nocytech/business-management/frontend/src/app/categories/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/nocytech/business-management/frontend/src/app/categories/page.tsx"],g={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/categories/page",pathname:"/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},517:(e,a,r)=>{Promise.resolve().then(r.bind(r,2207))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2207:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>y});var t=r(687),s=r(3210),n=r(646),i=r(2643),l=r(7576),o=r(1907),c=r(4926);let d=(0,r(2688).A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var g=r(3143),p=r(8233),u=r(6474),m=r(9270),x=r(5505),h=r(3213);function y(){let{user:e}=(0,h.A)(),[a,r]=(0,s.useState)([]),[y,v]=(0,s.useState)(!0),[f,j]=(0,s.useState)(!1),[b,A]=(0,s.useState)(!1),[k,w]=(0,s.useState)(null),[_,N]=(0,s.useState)({name:""}),[C,P]=(0,s.useState)({page:1,per_page:10}),[E,K]=(0,s.useState)(""),[S,z]=(0,s.useState)(""),U=async()=>{try{v(!0);let e=await x.U.getAll();r(e)}catch(e){console.error("Error loading categories:",e)}finally{v(!1)}},q=()=>{K(S),P(e=>({...e,page:1}))},M=e=>{z(e)},G=async()=>{try{let a={..._,organization_id:e?.organization_id||""};await x.U.create(a),j(!1),N({name:""}),U()}catch(e){console.error("Error creating category:",e)}},R=async()=>{if(k)try{let e={name:_.name};await x.U.update(k.id,e),A(!1),w(null),U()}catch(e){console.error("Error updating category:",e)}},D=async e=>{if(confirm("Bu kategoriyi silmek istediğinizden emin misiniz?"))try{await x.U.delete(e),U()}catch(e){console.error("Error deleting category:",e)}},L=e=>{w(e),N({name:e.name}),A(!0)},O=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),T=a.filter(e=>{if(!E)return!0;let a=O(E);return O(e.name).includes(a)}),$=[{key:"name",header:"Kategori",render:e=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.name})]})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>(0,t.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("tr-TR")})},{key:"actions",header:"İşlemler",render:e=>(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.A,{size:"sm",variant:"secondary",onClick:()=>L(e),children:(0,t.jsx)(g.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.A,{size:"sm",variant:"danger",onClick:()=>D(e.id),children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})}];return y?(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,t.jsx)(n.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kategoriler"}),(0,t.jsx)("p",{className:"text-gray-600",children:"\xdcr\xfcn kategorilerinizi y\xf6netin"})]}),(0,t.jsxs)(i.A,{onClick:()=>j(!0),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Yeni Kategori"]})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)("input",{type:"text",placeholder:"Kategori adı ara...",value:S,onChange:e=>M(e.target.value),onKeyDown:e=>"Enter"===e.key&&q(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsx)(i.A,{onClick:q,variant:"primary",className:"px-4 py-2",children:"Ara"}),E&&(0,t.jsx)(i.A,{onClick:()=>{K(""),z(""),P(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),E&&(0,t.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[T.length,' kategori g\xf6steriliyor • "',E,'" araması']})]}),(0,t.jsx)(c.A,{columns:$,data:T,pagination:{page:C.page,per_page:C.per_page,total:T.length,total_pages:Math.ceil(T.length/C.per_page),has_next:C.page<Math.ceil(T.length/C.per_page),has_prev:C.page>1},onPageChange:e=>{P(a=>({...a,page:e}))},onPerPageChange:e=>{P({page:1,per_page:e})},loading:y,emptyMessage:"Hen\xfcz kategori kaydı bulunmuyor",emptyIcon:(0,t.jsx)(d,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0}),(0,t.jsx)(l.A,{isOpen:f,onClose:()=>j(!1),title:"Yeni Kategori Ekle",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(o.A,{label:"Kategori Adı",value:_.name,onChange:e=>N({..._,name:e.target.value}),placeholder:"Kategori adını girin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>j(!1),children:"İptal"}),(0,t.jsx)(i.A,{onClick:G,children:"Kategori Ekle"})]})]})}),(0,t.jsx)(l.A,{isOpen:b,onClose:()=>A(!1),title:"Kategori D\xfczenle",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(o.A,{label:"Kategori Adı",value:_.name,onChange:e=>N({..._,name:e.target.value}),placeholder:"Kategori adını girin"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,t.jsx)(i.A,{variant:"secondary",onClick:()=>A(!1),children:"İptal"}),(0,t.jsx)(i.A,{onClick:R,children:"G\xfcncelle"})]})]})})]})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5505:(e,a,r)=>{"use strict";r.d(a,{U:()=>s});var t=r(2185);let s={getAll:async()=>(await t.u.get("/categories")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get(`/categories/paginated?${a}`)},async getById(e){try{return(await t.u.get(`/categories/${e}`)).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await t.u.post("/categories",e)},async update(e,a){await t.u.put(`/categories/${e}`,a)},async delete(e){await t.u.delete(`/categories/${e}`)}}},7365:(e,a,r)=>{Promise.resolve().then(r.bind(r,8911))},8911:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/categories/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/categories/page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var a=require("../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[169,798,71,480,248],()=>r(199));module.exports=t})();