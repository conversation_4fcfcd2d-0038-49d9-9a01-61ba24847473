(()=>{var e={};e.id=363,e.ids=[363],e.modules={213:(e,s,t)=>{Promise.resolve().then(t.bind(t,7133))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3539:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/nocytech/business-management/frontend/src/app/debts/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/nocytech/business-management/frontend/src/app/debts/[id]/page.tsx","default")},3613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3698:(e,s,t)=>{"use strict";t.d(s,{J:()=>a});var r=t(2185);let a={getAll:async()=>(await r.u.get("/debts")).data||[],async getPaginated(e){let s=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get(`/debts/paginated?${s}`)},async getById(e){try{return(await r.u.get(`/debts/${e}`)).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await r.u.post("/debts",e)},async update(e,s){await r.u.put(`/debts/${e}`,s)},async delete(e){await r.u.delete(`/debts/${e}`)},async pay(e,s){await r.u.post(`/debts/${e}/pay`,s)},getUnpaid:async()=>(await r.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await r.u.get("/debts/paid")).data||[],async getByDateRange(e,s){let t=new URLSearchParams;return e&&t.append("start_date",e),s&&t.append("end_date",s),(await r.u.get(`/debts/filter/date-range?${t.toString()}`)).data||[]},getByPaymentStatus:async e=>(await r.u.get(`/debts/filter/payment-status?is_paid=${e}`)).data||[]}},3873:e=>{"use strict";e.exports=require("path")},4807:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(5239),a=t(8088),n=t(8170),i=t.n(n),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["debts",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3539)),"/Users/<USER>/nocytech/business-management/frontend/src/app/debts/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/nocytech/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/nocytech/business-management/frontend/src/app/debts/[id]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debts/[id]/page",pathname:"/debts/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7133:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(687),a=t(3210),n=t(6189),i=t(2977),l=t(2643),d=t(1170),c=t(3698),o=t(4780),m=t(5778),x=t(3143),u=t(8233),p=t(1312),h=t(8340),g=t(3928),b=t(228),j=t(3613),y=t(3576);function v(){let e=(0,n.useParams)().id,[s,t]=(0,a.useState)(null),[v,f]=(0,a.useState)(!0),[N,w]=(0,a.useState)(null);(0,a.useCallback)(async()=>{try{f(!0),w(null);let s=await c.J.getById(e);if(!s)return void w("Bor\xe7 bulunamadı.");t(s)}catch(e){console.error("Error loading debt:",e),w("Bor\xe7 bilgileri y\xfcklenirken bir hata oluştu.")}finally{f(!1)}},[e]);let _=()=>s?`${s.name} ${s.surname}`.trim():"",k=s&&(0,r.jsxs)(r.Fragment,{children:[!s.is_paid&&(0,r.jsxs)(l.A,{variant:"warning",onClick:()=>{},className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"\xd6deme Yap"]}),(0,r.jsxs)(l.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,r.jsxs)(l.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,r.jsx)(i.Ay,{title:`Bor\xe7 #${s?.id.slice(-8)||"Detayı"}`,subtitle:"Bor\xe7 bilgilerini g\xf6r\xfcnt\xfcleyin",loading:v,error:N,backUrl:"/debts",actions:k,children:s&&(0,r.jsxs)(i.A7,{columns:2,children:[(0,r.jsx)(i.JH,{title:"Bor\xe7lu Bilgileri",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.Qn,{label:"Ad Soyad",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:_()})]})}),(0,r.jsx)(i.Qn,{label:"Ad",value:s.name}),(0,r.jsx)(i.Qn,{label:"Soyad",value:s.surname}),(0,r.jsx)(i.Qn,{label:"Telefon",value:s.phone?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,r.jsx)("a",{href:`tel:${s.phone}`,className:"text-purple-600 hover:text-purple-800 font-medium",children:(0,o.qH)(s.phone)})]}):(0,r.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})})]})}),(0,r.jsx)(i.JH,{title:"Bor\xe7 Bilgileri",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.Qn,{label:"Bor\xe7 Tutarı",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,r.jsxs)("span",{className:"font-bold text-red-600 text-lg",children:["₺",s.amount.toLocaleString("tr-TR")]})]})}),(0,r.jsx)(i.Qn,{label:"\xd6deme Durumu",value:s?s.is_paid?(0,r.jsx)(d.E,{variant:"success",children:"\xd6dendi"}):(0,r.jsx)(d.E,{variant:"danger",children:"\xd6denmedi"}):null}),(0,r.jsx)(i.Qn,{label:"Oluşturulma",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,r.jsx)("span",{children:(0,o.Yq)(s.created_at)})]})}),(0,r.jsx)(i.Qn,{label:"Son G\xfcncelleme",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,r.jsx)("span",{children:(0,o.Yq)(s.updated_at)})]})})]})}),(0,r.jsxs)(i.JH,{title:"Bor\xe7 Durumu",className:"md:col-span-2",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:`text-center p-4 rounded-lg ${s.is_paid?"bg-green-50":"bg-red-50"}`,children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Durum"}),(0,r.jsx)("div",{className:`text-lg font-semibold ${s.is_paid?"text-green-600":"text-red-600"}`,children:s.is_paid?"\xd6dendi":"\xd6denmedi"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Bor\xe7 Tutarı"}),(0,r.jsxs)("div",{className:"text-lg font-semibold text-blue-600",children:["₺",s.amount.toLocaleString("tr-TR")]})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Bor\xe7lu"}),(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-700",children:_()})]})]}),!s.is_paid&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,r.jsx)("strong",{children:"\xd6deme Bekliyor:"})," Bu bor\xe7 hen\xfcz \xf6denmemiştir. \xd6deme yapıldığında otomatik olarak kasaya eklenecektir."]})]})}),s.is_paid&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-sm text-green-800",children:[(0,r.jsx)("strong",{children:"\xd6deme Tamamlandı:"})," Bu bor\xe7 başarıyla \xf6denmiştir. \xd6deme tutarı kasaya eklenmiştir."]})]})})]}),(0,r.jsx)(i.JH,{title:"Bor\xe7 \xd6zeti",className:"md:col-span-2",children:(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(y.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"Bor\xe7 Fişi"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Bor\xe7lu:"})," ",_()]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Telefon:"})," ",s.phone?(0,o.qH)(s.phone):"Belirtilmemiş"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Tutar:"})," ₺",s.amount.toLocaleString("tr-TR")]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Durum:"})," ",s.is_paid?"\xd6dendi":"\xd6denmedi"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Kayıt Tarihi:"})," ",(0,o.Yq)(s.created_at)]}),s.is_paid&&(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"\xd6deme Tarihi:"})," ",(0,o.Yq)(s.updated_at)]})]})]})})]})})}},8357:(e,s,t)=>{Promise.resolve().then(t.bind(t,3539))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[169,798,480,145],()=>t(4807));module.exports=r})();