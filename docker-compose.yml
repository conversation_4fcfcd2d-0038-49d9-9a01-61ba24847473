version: "3.8"

services:
  businessdb:
    image: postgres:16
    container_name: bm_postgres
    environment:
      - POSTGRES_USER=business
      - POSTGRES_PASSWORD=business
      - POSTGRES_DB=business
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "5463:5432"

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: bm_backend
    volumes:
      - ./backend:/app
    ports:
      - "5555:5555"
    depends_on:
      - businessdb
    environment:
      - DB_HOST=businessdb
      - DB_PORT=5432
      - DB_USER=business
      - DB_PASSWORD=business
      - DB_NAME=business

volumes:
  pgdata: